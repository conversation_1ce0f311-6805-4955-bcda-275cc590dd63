# 达梦数据库自动同步脚本说明

## 功能概述
这套脚本可以自动获取达梦数据库中的所有表名，然后逐个将表数据同步到MySQL数据库中。

## 文件说明

### 1. dm_get_all_tables.ktr
- **功能**: 获取达梦数据库中所有用户表的名称
- **输出**: 生成 `table_list.txt` 文件，包含所有表名（每行一个表名）

### 2. dm_sync_single_table.ktr
- **功能**: 同步单个表的数据从达梦到MySQL
- **参数**: TABLE_NAME（要同步的表名）
- **特点**: 使用TRUNCATE方式，完全替换目标表数据

### 3. dm_sync_single_table_job.kjb
- **功能**: 单表同步的作业包装器
- **参数**: TABLE_NAME（要同步的表名）

### 4. dm_read_tables_and_sync.ktr
- **功能**: 读取表名文件，并对每个表执行同步作业
- **特点**: 逐行读取表名，为每个表名调用同步作业

### 5. dm_auto_sync_all_tables.kjb
- **功能**: 主作业文件，完整的自动同步流程
- **流程**: 获取表名 → 读取表名并逐个同步

### 改进版本（解决参数传递问题）
### 6. dm_simple_auto_sync.ktr
- **功能**: 最简单的自动同步脚本（强烈推荐）
- **特点**: 直接在一个转换中完成所有工作，参数传递最可靠
- **流程**: 获取表名 → 直接调用TransExecutor同步

### 7. dm_read_tables_and_sync_v2.ktr
- **功能**: 改进版的批量同步转换
- **特点**: 使用TransExecutor而不是JobExecutor，参数传递更稳定

### 8. dm_auto_sync_all_tables_v2.kjb
- **功能**: 改进版的主作业文件
- **特点**: 使用改进版的批量同步转换

## 使用方法

### 方法一：最简单的方式（强烈推荐）
```bash
# 一个转换完成所有工作，参数传递最可靠
kitchen.sh -file=dm_simple_auto_sync.ktr
```

### 方法二：完全自动化（改进版）
```bash
# 运行改进版主作业，使用TransExecutor而不是JobExecutor
kitchen.sh -file=dm_auto_sync_all_tables_v2.kjb
```

### 方法三：原版本（如果参数传递有问题，不推荐）
```bash
# 运行主作业，自动完成所有步骤
kitchen.sh -file=dm_auto_sync_all_tables.kjb
```

### 方法四：分步执行
```bash
# 1. 先获取所有表名
kitchen.sh -file=dm_get_all_tables.ktr

# 2. 然后执行批量同步（改进版）
kitchen.sh -file=dm_read_tables_and_sync_v2.ktr
```

### 方法五：同步单个表
```bash
# 同步指定的单个表
kitchen.sh -file=dm_sync_single_table_job.kjb -param:TABLE_NAME=your_table_name
```

## 配置要求

### 1. 数据库连接配置
在使用前，需要修改以下文件中的数据库连接信息：

#### 达梦数据库连接（在所有.ktr文件中）
```xml
<connection>
  <name>DM_DB</name>
  <server>localhost</server>        <!-- 修改为实际服务器地址 -->
  <database>DAMENG</database>       <!-- 修改为实际数据库名 -->
  <port>5236</port>                 <!-- 修改为实际端口 -->
  <username>SYSDBA</username>       <!-- 修改为实际用户名 -->
  <password>your_password</password> <!-- 修改为实际密码 -->
</connection>
```

#### MySQL数据库连接（在同步脚本中）
```xml
<connection>
  <name>MYSQL_DB</name>
  <server>localhost</server>        <!-- 修改为实际服务器地址 -->
  <database>sync_db</database>      <!-- 修改为实际数据库名 -->
  <port>3306</port>                 <!-- 修改为实际端口 -->
  <username>root</username>         <!-- 修改为实际用户名 -->
  <password>your_password</password> <!-- 修改为实际密码 -->
</connection>
```

### 2. 驱动程序
确保以下JDBC驱动文件在Kettle的lib目录中：
- 达梦数据库驱动：`DmJdbcDriver18.jar`
- MySQL驱动：`mysql-connector-java-8.0.x.jar`

### 3. 目标表结构
确保MySQL数据库中已经创建了与达梦数据库相同结构的表。可以使用之前提供的 `mysql_create_tables.sql` 脚本。

## 工作流程

1. **获取表名阶段**
   - 连接达梦数据库
   - 执行 `SELECT TABLE_NAME FROM USER_TABLES ORDER BY TABLE_NAME`
   - 将结果写入 `table_list.txt` 文件

2. **批量同步阶段**
   - 读取 `table_list.txt` 文件
   - 对每个表名执行以下操作：
     - 从达梦数据库读取表数据：`SELECT * FROM ${TABLE_NAME}`
     - 清空MySQL目标表：`TRUNCATE TABLE ${TABLE_NAME}`
     - 将数据写入MySQL表

## 注意事项

### 1. 数据安全
- 脚本使用 `TRUNCATE` 方式，会完全清空目标表后再插入数据
- 建议在测试环境先验证，确认无误后再在生产环境使用
- 建议在同步前备份MySQL目标数据库

### 2. 性能考虑
- 大表同步可能需要较长时间
- 可以通过修改 `commit` 参数调整批量提交大小（默认1000条）
- 建议在业务低峰期执行

### 3. 错误处理
- 如果某个表同步失败，不会影响其他表的同步
- 查看Kettle日志了解具体错误信息
- 可以单独重新同步失败的表

### 4. 表结构要求
- 两个数据库的表结构必须兼容
- 字段名称必须一致
- 数据类型必须兼容

## 自定义配置

### 1. 过滤特定表
如果需要排除某些表，可以修改 `dm_get_all_tables.ktr` 中的SQL：
```sql
SELECT TABLE_NAME 
FROM USER_TABLES 
WHERE TABLE_NAME NOT IN ('EXCLUDED_TABLE1', 'EXCLUDED_TABLE2')
ORDER BY TABLE_NAME
```

### 2. 增量同步
如果需要增量同步而不是全量替换，可以修改 `dm_sync_single_table.ktr` 中的设置：
- 将 `<truncate>Y</truncate>` 改为 `<truncate>N</truncate>`
- 根据需要配置主键或唯一键处理

### 3. 并行同步
如果需要并行同步多个表以提高效率，可以：
- 修改 `dm_read_tables_and_sync.ktr` 中的 `<copies>1</copies>` 增加并行度
- 注意数据库连接数限制

## 故障排除

### 常见问题
1. **连接失败**: 检查数据库服务状态和连接参数
2. **表不存在**: 确保MySQL中已创建对应的表
3. **权限不足**: 确保数据库用户有足够的读写权限
4. **内存不足**: 对于大表，可能需要调整JVM内存参数

### 日志查看
- Kettle执行日志：查看控制台输出
- 数据库日志：检查数据库服务器日志
- 生成的文件：检查 `table_list.txt` 是否正确生成

## 版本信息
- 适用于：Kettle 9.1
- 达梦数据库：DM7/DM8
- MySQL：5.7+/8.0+
- 创建日期：2024-07-30
