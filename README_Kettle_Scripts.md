# 达梦数据库到MySQL同步脚本说明

## 文件说明

### 标准版本（如果遇到兼容性问题，请使用简化版本）
### 1. dm_table_query.ktr
- **功能**: 查询达梦数据库中的表结构信息
- **输出**: 生成包含表名、字段名、数据类型等信息的文本文件
- **适用版本**: Kettle 9.1

### 2. dm_to_mysql_sync.ktr
- **功能**: 单表数据从达梦数据库同步到MySQL
- **参数**: TABLE_NAME (要同步的表名)
- **适用版本**: Kettle 9.1

### 3. dm_to_mysql_batch_sync.kjb
- **功能**: 批量同步多个表的作业文件
- **包含表**: mpe_config, mpe_examinfo, mpe_examsessioninfo
- **适用版本**: Kettle 9.1

### 简化版本（推荐使用，兼容性更好）
### 4. dm_table_query_simple.ktr
- **功能**: 查询达梦数据库中的表结构信息（简化版）
- **输出**: 生成包含表名、字段名、数据类型等信息的文本文件
- **适用版本**: Kettle 9.1
- **特点**: 去除了复杂的日志配置，提高兼容性

### 5. dm_to_mysql_sync_simple.ktr
- **功能**: 单表数据从达梦数据库同步到MySQL（简化版）
- **参数**: TABLE_NAME (要同步的表名)
- **适用版本**: Kettle 9.1
- **特点**: 直接连接，无中间转换步骤

### 6. dm_to_mysql_batch_sync_simple.kjb
- **功能**: 批量同步多个表的作业文件（简化版）
- **包含表**: mpe_config, mpe_examinfo, mpe_examsessioninfo
- **适用版本**: Kettle 9.1
- **特点**: 简化的作业配置，更好的兼容性

## 使用前准备

### 1. 数据库连接配置

#### 达梦数据库连接 (DM_DB)
```
服务器: localhost
端口: 5236
数据库: DAMENG
用户名: SYSDBA
密码: [需要修改为实际密码]
驱动类: dm.jdbc.driver.DmDriver
连接URL: jdbc:dm://localhost:5236
```

#### MySQL数据库连接 (MYSQL_DB)
```
服务器: localhost
端口: 3306
数据库: sync_db
用户名: root
密码: [需要修改为实际密码]
```

### 2. 驱动程序准备
- 达梦数据库JDBC驱动: DmJdbcDriver18.jar
- MySQL JDBC驱动: mysql-connector-java-8.0.x.jar

将驱动文件放置到Kettle安装目录的lib文件夹中。

### 3. MySQL目标表创建
在MySQL中创建对应的目标表，字段类型映射如下：

#### 达梦到MySQL数据类型映射
| 达梦类型 | MySQL类型 | 说明 |
|---------|-----------|------|
| VARCHAR | VARCHAR | 字符串类型 |
| INT | INT | 整数类型 |
| TINYINT | TINYINT | 小整数类型 |
| TIMESTAMP | TIMESTAMP | 时间戳类型 |
| DECIMAL | DECIMAL | 精确数值类型 |

## 使用方法

### 1. 查询表结构
```bash
# 在Kettle中运行
kitchen.sh -file=dm_table_query.ktr
```

### 2. 单表同步
```bash
# 同步指定表
kitchen.sh -file=dm_to_mysql_sync.ktr -param:TABLE_NAME=mpe_config
```

### 3. 批量同步
```bash
# 批量同步多个表
kitchen.sh -file=dm_to_mysql_batch_sync.kjb
```

## 注意事项

### 1. 数据库连接
- 确保达梦数据库和MySQL数据库都能正常连接
- 检查网络连通性和防火墙设置
- 验证用户权限是否足够

### 2. 数据类型兼容性
- 时间戳字段可能需要特殊处理
- 大文本字段(CLOB/TEXT)需要注意长度限制
- 数值精度可能存在差异

### 3. 性能优化
- 大表同步建议分批处理
- 可以设置提交间隔(commit size)
- 考虑在非业务高峰期执行

### 4. 错误处理
- 检查日志文件了解同步状态
- 数据冲突时的处理策略
- 网络中断时的重试机制

## 脚本特性

### Kettle 9.1 兼容性
- 使用标准的Kettle 9.1组件
- 避免使用高版本特有功能
- XML格式符合9.1版本规范

### 参数化设计
- 支持通过参数指定表名
- 便于批量处理和自动化
- 易于维护和扩展

### 错误处理
- 包含基本的错误处理机制
- 支持事务回滚
- 提供详细的日志记录

## 扩展说明

### 添加新表同步
1. 在批量同步作业中添加新的转换步骤
2. 设置对应的TABLE_NAME参数
3. 确保MySQL中存在对应的目标表

### 自定义数据转换
1. 在"数据类型转换"步骤中添加字段映射
2. 处理特殊的数据格式转换
3. 添加数据清洗逻辑

### 增量同步
1. 添加时间戳字段过滤
2. 实现基于主键的更新策略
3. 考虑使用CDC(变更数据捕获)机制

## 故障排除

### 常见问题
1. **文件格式错误**:
   - 错误信息: "文件'xxx.ktr'不是Kettle的文件类型"
   - 解决方案: 使用简化版本的脚本文件（*_simple.ktr 或 *_simple.kjb）
   - 原因: 某些Kettle 9.1版本对XML格式要求较严格

2. **连接失败**: 检查数据库服务状态和连接参数
3. **驱动找不到**: 确认JDBC驱动文件位置
4. **权限不足**: 验证数据库用户权限
5. **数据类型错误**: 检查字段类型映射
6. **内存不足**: 调整JVM内存参数

### 推荐使用顺序
1. **首先尝试**: 简化版本脚本（*_simple.ktr, *_simple.kjb）
2. **如果简化版本正常**: 可以尝试标准版本获得更多功能
3. **如果都有问题**: 检查Kettle版本和JDBC驱动

### 日志查看
- Kettle日志文件位置: logs/目录
- 数据库连接日志
- 转换执行日志
- 错误详细信息

## 版本信息
- Kettle版本: 9.1
- 达梦数据库: 支持DM7/DM8
- MySQL版本: 5.7+/8.0+
- 创建日期: 2024-07-30
