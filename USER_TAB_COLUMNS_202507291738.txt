|TABLE_NAME         |COLUMN_NAME             |DATA_TYPE|DATA_LENGTH|NULLABLE|DATA_DEFAULT       |
|-------------------|------------------------|---------|-----------|--------|-------------------|
|mpe_config         |uid                     |VARCHAR  |128        |N       |                   |
|mpe_config         |ex_id                   |VARCHAR  |120        |N       |                   |
|mpe_config         |examiner_num            |INT      |4          |Y       |                   |
|mpe_config         |layout_time             |INT      |4          |Y       |                   |
|mpe_config         |examination_num         |INT      |4          |Y       |0                  |
|mpe_config         |seatting_way            |INT      |4          |Y       |                   |
|mpe_config         |first_seating           |INT      |4          |Y       |                   |
|mpe_config         |is_seat_unify           |INT      |4          |Y       |                   |
|mpe_config         |is_seatway_unify        |INT      |4          |Y       |1                  |
|mpe_config         |tour_rule               |VARCHAR  |48         |Y       |                   |
|mpe_config         |examinee_percent        |INT      |4          |Y       |                   |
|mpe_config         |city_opentime           |TIMESTAMP|8          |Y       |                   |
|mpe_config         |county_opentime         |TIMESTAMP|8          |Y       |                   |
|mpe_config         |exampoint_opentime      |TIMESTAMP|8          |Y       |                   |
|mpe_config         |layout_number           |INT      |4          |Y       |                   |
|mpe_config         |layout_begin_time       |INT      |4          |Y       |                   |
|mpe_config         |parent_uid              |VARCHAR  |128        |Y       |                   |
|mpe_config         |enable_children_orglevel|VARCHAR  |120        |Y       |                   |
|mpe_config         |examinee_data_status    |TINYINT  |1          |Y       |                   |
|mpe_config         |status                  |VARCHAR  |4          |N       |'A'                |
|mpe_config         |create_by               |VARCHAR  |128        |Y       |                   |
|mpe_config         |create_time             |TIMESTAMP|8          |Y       |                   |
|mpe_config         |update_by               |VARCHAR  |128        |Y       |                   |
|mpe_config         |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()|
|mpe_examinfo       |uid                     |VARCHAR  |128        |N       |                   |
|mpe_examinfo       |ex_id                   |VARCHAR  |120        |N       |                   |
|mpe_examinfo       |year                    |VARCHAR  |16         |N       |                   |
|mpe_examinfo       |et_id                   |VARCHAR  |24         |Y       |                   |
|mpe_examinfo       |name                    |VARCHAR  |400        |N       |                   |
|mpe_examinfo       |exam_abbreviation       |VARCHAR  |320        |Y       |                   |
|mpe_examinfo       |org_id                  |VARCHAR  |136        |N       |                   |
|mpe_examinfo       |archive                 |INT      |4          |N       |0                  |
|mpe_examinfo       |batch_no                |VARCHAR  |8          |Y       |                   |
|mpe_examinfo       |status                  |VARCHAR  |4          |N       |'A'                |
|mpe_examinfo       |create_by               |VARCHAR  |128        |Y       |                   |
|mpe_examinfo       |create_time             |TIMESTAMP|8          |Y       |                   |
|mpe_examinfo       |update_by               |VARCHAR  |128        |Y       |                   |
|mpe_examinfo       |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()|
|mpe_examsessioninfo|uid                     |VARCHAR  |128        |N       |                   |
|mpe_examsessioninfo|ex_id                   |VARCHAR  |120        |N       |                   |
|mpe_examsessioninfo|es_code                 |VARCHAR  |8          |N       |                   |
|mpe_examsessioninfo|name                    |VARCHAR  |200        |N       |                   |
|mpe_examsessioninfo|start_time              |TIMESTAMP|8          |N       |                   |
|mpe_examsessioninfo|end_time                |TIMESTAMP|8          |N       |                   |
|mpe_examsessioninfo|status                  |VARCHAR  |4          |N       |'A'                |
|mpe_examsessioninfo|create_by               |VARCHAR  |128        |Y       |                   |
|mpe_examsessioninfo|create_time             |TIMESTAMP|8          |Y       |                   |
|mpe_examsessioninfo|update_by               |VARCHAR  |128        |Y       |                   |
|mpe_examsessioninfo|update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()|
|mpe_examsubjectinfo|uid                     |VARCHAR  |200        |N       |                   |
|mpe_examsubjectinfo|ex_id                   |VARCHAR  |120        |N       |                   |
|mpe_examsubjectinfo|es_code                 |VARCHAR  |8          |Y       |                   |
|mpe_examsubjectinfo|subject_code            |VARCHAR  |40         |N       |                   |
|mpe_examsubjectinfo|name                    |VARCHAR  |200        |N       |                   |
|mpe_examsubjectinfo|start_time              |TIMESTAMP|8          |Y       |                   |
|mpe_examsubjectinfo|end_time                |TIMESTAMP|8          |Y       |                   |
|mpe_examsubjectinfo|sub_es_code             |VARCHAR  |12         |Y       |                   |
|mpe_examsubjectinfo|sub_es_name             |VARCHAR  |200        |Y       |                   |
|mpe_examsubjectinfo|sub_es_order            |VARCHAR  |8          |Y       |                   |
|mpe_examsubjectinfo|status                  |VARCHAR  |4          |N       |'A'                |
|mpe_examsubjectinfo|create_by               |VARCHAR  |128        |Y       |                   |
|mpe_examsubjectinfo|create_time             |TIMESTAMP|8          |Y       |                   |
|mpe_examsubjectinfo|update_by               |VARCHAR  |128        |Y       |                   |
|mpe_examsubjectinfo|update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()|
|mpp_building       |uid                     |VARCHAR  |200        |N       |                   |
|mpp_building       |building_name           |VARCHAR  |400        |N       |                   |
|mpp_building       |building_floors         |TINYINT  |1          |Y       |                   |
|mpp_building       |org_id                  |VARCHAR  |136        |N       |                   |
|mpp_building       |status                  |VARCHAR  |4          |N       |'A'                |
|mpp_building       |create_by               |VARCHAR  |128        |Y       |                   |
|mpp_building       |create_time             |TIMESTAMP|8          |Y       |                   |
|mpp_building       |update_by               |VARCHAR  |128        |Y       |                   |
|mpp_building       |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()|
|mpp_deviceinfo     |uid                     |VARCHAR  |160        |N       |                   |
|mpp_deviceinfo     |org_id                  |VARCHAR  |136        |N       |                   |
|mpp_deviceinfo     |sbdm                    |VARCHAR  |72         |Y       |                   |
|mpp_deviceinfo     |dt_code                 |VARCHAR  |20         |N       |                   |
|mpp_deviceinfo     |sbccbh                  |VARCHAR  |256        |Y       |                   |
|mpp_deviceinfo     |name                    |VARCHAR  |400        |Y       |                   |
|mpp_deviceinfo     |device_status           |VARCHAR  |8          |Y       |'1'                |
|mpp_deviceinfo     |dev_mfr                 |VARCHAR  |200        |Y       |                   |
|mpp_deviceinfo     |model                   |VARCHAR  |400        |Y       |                   |
|mpp_deviceinfo     |sbpp                    |VARCHAR  |80         |Y       |                   |
|mpp_deviceinfo     |parent_dev_id           |VARCHAR  |800        |Y       |                   |
|mpp_deviceinfo     |parent_dev_domain       |VARCHAR  |2,000      |Y       |                   |
|mpp_deviceinfo     |osd_port                |VARCHAR  |80         |Y       |                   |
|mpp_deviceinfo     |channel_num             |INT      |4          |Y       |                   |
|mpp_deviceinfo     |domain                  |VARCHAR  |2,000      |Y       |                   |
|mpp_deviceinfo     |dev_mac                 |VARCHAR  |128        |Y       |                   |
|mpp_deviceinfo     |dev_ip                  |VARCHAR  |80         |Y       |                   |
|mpp_deviceinfo     |dev_ipv6                |VARCHAR  |800        |Y       |                   |
|mpp_deviceinfo     |dev_port                |INT      |4          |Y       |                   |
|mpp_deviceinfo     |login_name              |VARCHAR  |200        |Y       |                   |
|mpp_deviceinfo     |login_pwd               |VARCHAR  |400        |Y       |                   |
|mpp_deviceinfo     |hardware_ver            |VARCHAR  |200        |Y       |                   |
|mpp_deviceinfo     |software_ver            |VARCHAR  |200        |Y       |                   |
|mpp_deviceinfo     |firmware_ver            |VARCHAR  |200        |Y       |                   |
|mpp_deviceinfo     |purchase_time           |TIMESTAMP|8          |Y       |                   |
|mpp_deviceinfo     |warranty_time           |TIMESTAMP|8          |Y       |                   |
|mpp_deviceinfo     |scrap_year              |VARCHAR  |12         |Y       |                   |
|mpp_deviceinfo     |cam_resomax             |VARCHAR  |120        |Y       |                   |
|mpp_deviceinfo     |osd_max                 |INT      |4          |Y       |0                  |
|mpp_deviceinfo     |verify_ways             |VARCHAR  |80         |Y       |                   |
|mpp_deviceinfo     |block                   |TINYINT  |1          |Y       |                   |
|mpp_deviceinfo     |wxdpdm                  |VARCHAR  |200        |Y       |                   |
|mpp_deviceinfo     |rtsp                    |VARCHAR  |920        |Y       |                   |
|mpp_deviceinfo     |function_type           |VARCHAR  |8          |Y       |                   |
|mpp_deviceinfo     |sim                     |VARCHAR  |512        |Y       |                   |
|mpp_deviceinfo     |ar_code                 |VARCHAR  |24         |Y       |                   |
|mpp_deviceinfo     |remark                  |VARCHAR  |2,000      |Y       |                   |
|mpp_deviceinfo     |status                  |VARCHAR  |4          |N       |'A'                |
|mpp_deviceinfo     |create_by               |VARCHAR  |128        |Y       |                   |
|mpp_deviceinfo     |create_time             |TIMESTAMP|8          |Y       |                   |
|mpp_deviceinfo     |update_by               |VARCHAR  |128        |Y       |                   |
|mpp_deviceinfo     |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()|
|mpp_exam_area      |uid                     |VARCHAR  |128        |N       |                   |
|mpp_exam_area      |ex_id                   |VARCHAR  |120        |N       |                   |
|mpp_exam_area      |examarea_no             |VARCHAR  |80         |N       |                   |
|mpp_exam_area      |examarea_name           |VARCHAR  |320        |Y       |                   |
|mpp_exam_area      |org_id                  |VARCHAR  |136        |N       |                   |
|mpp_exam_area      |examarea_type           |VARCHAR  |4          |Y       |                   |
|mpp_exam_area      |examarea_level          |VARCHAR  |4          |Y       |                   |
|mpp_exam_area      |parent_uid              |VARCHAR  |128        |N       |                   |
|mpp_exam_area      |examorg_id_1            |VARCHAR  |128        |Y       |                   |
|mpp_exam_area      |examorg_id_2            |VARCHAR  |128        |Y       |                   |
|mpp_exam_area      |examorg_id_3            |VARCHAR  |128        |Y       |                   |
|mpp_exam_area      |examorg_id_4            |VARCHAR  |128        |Y       |                   |
|mpp_exam_area      |create_by               |VARCHAR  |128        |Y       |                   |
|mpp_exam_area      |create_time             |TIMESTAMP|8          |Y       |                   |
|mpp_exam_area      |update_by               |VARCHAR  |128        |Y       |                   |
|mpp_exam_area      |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()|
|mpp_exam_point     |uid                     |VARCHAR  |128        |N       |                   |
|mpp_exam_point     |ex_id                   |VARCHAR  |120        |N       |                   |
|mpp_exam_point     |exam_point_code         |VARCHAR  |320        |Y       |                   |
|mpp_exam_point     |exam_point_name         |VARCHAR  |320        |Y       |                   |
|mpp_exam_point     |org_id                  |VARCHAR  |136        |N       |                   |
|mpp_exam_point     |parent_examorg_id       |VARCHAR  |200        |Y       |                   |
|mpp_exam_point     |point_setup_type        |TINYINT  |1          |Y       |0                  |
|mpp_exam_point     |examroom_num            |INT      |4          |Y       |                   |
|mpp_exam_point     |examroom_backup_num     |INT      |4          |Y       |                   |
|mpp_exam_point     |end_point               |TINYINT  |1          |Y       |0                  |
|mpp_exam_point     |hand_type               |VARCHAR  |4          |Y       |'2'                |
|mpp_exam_point     |kdytdm                  |VARCHAR  |8          |Y       |'11'               |
|mpp_exam_point     |bz                      |VARCHAR  |1,020      |Y       |                   |
|mpp_exam_point     |zbfkpb                  |VARCHAR  |8          |Y       |                   |
|mpp_exam_point     |zbfkpb_5g               |VARCHAR  |8          |Y       |                   |
|mpp_exam_point     |examorg_id_1            |VARCHAR  |128        |Y       |                   |
|mpp_exam_point     |examorg_id_2            |VARCHAR  |128        |Y       |                   |
|mpp_exam_point     |examorg_id_3            |VARCHAR  |128        |Y       |                   |
|mpp_exam_point     |create_by               |VARCHAR  |128        |Y       |                   |
|mpp_exam_point     |create_time             |TIMESTAMP|8          |Y       |                   |
|mpp_exam_point     |update_by               |VARCHAR  |128        |Y       |                   |
|mpp_exam_point     |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()|
|mpp_logicexamroom  |uid                     |VARCHAR  |128        |N       |                   |
|mpp_logicexamroom  |ex_id                   |VARCHAR  |120        |N       |                   |
|mpp_logicexamroom  |es_code                 |VARCHAR  |8          |N       |                   |
|mpp_logicexamroom  |logic_exr_no            |VARCHAR  |200        |N       |                   |
|mpp_logicexamroom  |logic_name              |VARCHAR  |160        |Y       |                   |
|mpp_logicexamroom  |examinee_qty            |INT      |4          |Y       |                   |
|mpp_logicexamroom  |bpkldm                  |VARCHAR  |40         |Y       |                   |
|mpp_logicexamroom  |org_id                  |VARCHAR  |136        |N       |                   |
|mpp_logicexamroom  |exampoint_uid           |VARCHAR  |128        |Y       |                   |
|mpp_logicexamroom  |kch                     |VARCHAR  |32         |Y       |                   |
|mpp_logicexamroom  |status                  |VARCHAR  |4          |N       |'A'                |
|mpp_logicexamroom  |create_by               |VARCHAR  |128        |Y       |                   |
|mpp_logicexamroom  |create_time             |TIMESTAMP|8          |Y       |                   |
|mpp_logicexamroom  |update_by               |VARCHAR  |128        |Y       |                   |
|mpp_logicexamroom  |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()|
|mpp_orginfo        |uid                     |VARCHAR  |136        |N       |                   |
|mpp_orginfo        |org_code                |VARCHAR  |200        |N       |                   |
|mpp_orginfo        |org_display_code        |VARCHAR  |200        |Y       |                   |
|mpp_orginfo        |org_ident_code          |VARCHAR  |32         |N       |                   |
|mpp_orginfo        |org_type                |VARCHAR  |4          |N       |                   |
|mpp_orginfo        |org_level               |VARCHAR  |4          |Y       |                   |
|mpp_orginfo        |jgbsm                   |VARCHAR  |200        |Y       |                   |
|mpp_orginfo        |org_name                |VARCHAR  |200        |N       |                   |
|mpp_orginfo        |org_alias               |VARCHAR  |80         |Y       |                   |
|mpp_orginfo        |contact_name            |VARCHAR  |80         |Y       |                   |
|mpp_orginfo        |contact_tel             |VARCHAR  |80         |Y       |                   |
|mpp_orginfo        |technician_name         |VARCHAR  |80         |Y       |                   |
|mpp_orginfo        |technician_tel          |VARCHAR  |80         |Y       |                   |
|mpp_orginfo        |postal_code             |VARCHAR  |24         |Y       |                   |
|mpp_orginfo        |xxdm                    |VARCHAR  |32         |Y       |                   |
|mpp_orginfo        |yxlxdm                  |VARCHAR  |8          |Y       |                   |
|mpp_orginfo        |school_type             |TINYINT  |1          |Y       |                   |
|mpp_orginfo        |exam_area_code          |VARCHAR  |320        |Y       |                   |
|mpp_orginfo        |exam_area_name          |VARCHAR  |320        |Y       |                   |
|mpp_orginfo        |exam_point_type         |VARCHAR  |4          |Y       |                   |
|mpp_orginfo        |exam_point_code         |VARCHAR  |320        |Y       |                   |
|mpp_orginfo        |exam_point_name         |VARCHAR  |320        |Y       |                   |
|mpp_orginfo        |org_photo_url           |VARCHAR  |800        |Y       |                   |
|mpp_orginfo        |org_signature_url       |VARCHAR  |800        |Y       |                   |
|mpp_orginfo        |address                 |VARCHAR  |800        |Y       |                   |
|mpp_orginfo        |latitude                |VARCHAR  |80         |Y       |                   |
|mpp_orginfo        |longitude               |VARCHAR  |80         |Y       |                   |
|mpp_orginfo        |fax                     |VARCHAR  |160        |Y       |                   |
|mpp_orginfo        |web_address             |VARCHAR  |400        |Y       |                   |
|mpp_orginfo        |email                   |VARCHAR  |400        |Y       |                   |
|mpp_orginfo        |parent_uid              |VARCHAR  |136        |N       |                   |
|mpp_orginfo                   |org_id_1                |VARCHAR  |136        |Y       |                     |
|mpp_orginfo                   |org_id_2                |VARCHAR  |136        |Y       |                     |
|mpp_orginfo                   |org_id_3                |VARCHAR  |136        |Y       |                     |
|mpp_orginfo                   |org_id_4                |VARCHAR  |136        |Y       |                     |
|mpp_orginfo                   |status                  |VARCHAR  |4          |N       |'A'                  |
|mpp_orginfo                   |create_by               |VARCHAR  |128        |Y       |                     |
|mpp_orginfo                   |create_time             |TIMESTAMP|8          |N       |                     |
|mpp_orginfo                   |update_by               |VARCHAR  |128        |Y       |                     |
|mpp_orginfo                   |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|mpp_placeinfo                 |uid                     |VARCHAR  |200        |N       |                     |
|mpp_placeinfo                 |org_id                  |VARCHAR  |136        |N       |                     |
|mpp_placeinfo                 |csdm                    |VARCHAR  |80         |N       |'1'                  |
|mpp_placeinfo                 |csbsm                   |VARCHAR  |80         |N       |                     |
|mpp_placeinfo                 |pt_code                 |VARCHAR  |8          |N       |                     |
|mpp_placeinfo                 |building_id             |VARCHAR  |128        |Y       |                     |
|mpp_placeinfo                 |building_floors         |TINYINT  |1          |Y       |                     |
|mpp_placeinfo                 |name                    |VARCHAR  |400        |Y       |                     |
|mpp_placeinfo                 |address                 |VARCHAR  |2,000      |Y       |                     |
|mpp_placeinfo                 |room_layout             |TINYINT  |1          |Y       |                     |
|mpp_placeinfo                 |room_cap                |SMALLINT |2          |Y       |                     |
|mpp_placeinfo                 |defaultseatting_way     |SMALLINT |2          |Y       |1                    |
|mpp_placeinfo                 |room_jc                 |VARCHAR  |200        |Y       |                     |
|mpp_placeinfo                 |contact_tel             |VARCHAR  |160        |Y       |                     |
|mpp_placeinfo                 |sec_person              |VARCHAR  |200        |Y       |                     |
|mpp_placeinfo                 |contact_phone           |VARCHAR  |44         |Y       |                     |
|mpp_placeinfo                 |longitude               |VARCHAR  |80         |Y       |                     |
|mpp_placeinfo                 |latitude                |VARCHAR  |80         |Y       |                     |
|mpp_placeinfo                 |status                  |VARCHAR  |4          |N       |'A'                  |
|mpp_placeinfo                 |create_by               |VARCHAR  |128        |Y       |                     |
|mpp_placeinfo                 |create_time             |TIMESTAMP|8          |Y       |CURRENT_TIMESTAMP()  |
|mpp_placeinfo                 |update_by               |VARCHAR  |128        |Y       |                     |
|mpp_placeinfo                 |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|mpp_rel_device_place          |uid                     |VARCHAR  |148        |N       |                     |
|mpp_rel_device_place          |pla_id                  |VARCHAR  |200        |N       |                     |
|mpp_rel_device_place          |org_id                  |VARCHAR  |200        |N       |                     |
|mpp_rel_device_place          |camera_pos              |VARCHAR  |8          |Y       |                     |
|mpp_rel_device_place          |dev_id                  |VARCHAR  |160        |N       |                     |
|mpp_rel_device_place          |dev_pos                 |VARCHAR  |200        |Y       |                     |
|mpp_rel_device_place          |create_by               |VARCHAR  |128        |Y       |                     |
|mpp_rel_device_place          |create_time             |TIMESTAMP|8          |Y       |                     |
|mpp_rel_device_place          |update_by               |VARCHAR  |128        |Y       |                     |
|mpp_rel_device_place          |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|mpp_rel_logicexamroom_subject |uid                     |VARCHAR  |128        |N       |                     |
|mpp_rel_logicexamroom_subject |ex_id                   |VARCHAR  |120        |Y       |                     |
|mpp_rel_logicexamroom_subject |logic_exr_no            |VARCHAR  |200        |Y       |                     |
|mpp_rel_logicexamroom_subject |es_code                 |VARCHAR  |8          |Y       |                     |
|mpp_rel_logicexamroom_subject |org_id                  |VARCHAR  |136        |Y       |                     |
|mpp_rel_logicexamroom_subject |subject_code            |VARCHAR  |40         |Y       |                     |
|mpp_rel_logicexamroom_subject |create_by               |VARCHAR  |128        |Y       |                     |
|mpp_rel_logicexamroom_subject |create_time             |TIMESTAMP|8          |Y       |                     |
|mpp_rel_logicexamroom_subject |update_by               |VARCHAR  |128        |Y       |                     |
|mpp_rel_logicexamroom_subject |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|mpp_rel_physicalexamroomenable|uid                     |VARCHAR  |128        |N       |                     |
|mpp_rel_physicalexamroomenable|ex_id                   |VARCHAR  |120        |N       |                     |
|mpp_rel_physicalexamroomenable|es_code                 |VARCHAR  |8          |N       |                     |
|mpp_rel_physicalexamroomenable|org_id                  |VARCHAR  |136        |N       |                     |
|mpp_rel_physicalexamroomenable|pla_id                  |VARCHAR  |200        |N       |                     |
|mpp_rel_physicalexamroomenable|seatting_way            |VARCHAR  |8          |Y       |                     |
|mpp_rel_physicalexamroomenable|first_seating           |VARCHAR  |4          |Y       |                     |
|mpp_rel_physicalexamroomenable|logic_exr_no            |VARCHAR  |80         |Y       |                     |
|mpp_rel_physicalexamroomenable|is_wc_pexroom           |INT      |4          |Y       |                     |
|mpp_rel_physicalexamroomenable|finish_dis              |INT      |4          |Y       |                     |
|mpp_rel_physicalexamroomenable|is_back_exroom          |SMALLINT |2          |Y       |0                    |
|mpp_rel_physicalexamroomenable|bykclx                  |VARCHAR  |4          |Y       |                     |
|mpp_rel_physicalexamroomenable|back_name               |VARCHAR  |120        |Y       |                     |
|mpp_rel_physicalexamroomenable|status                  |VARCHAR  |4          |N       |'A'                  |
|mpp_rel_physicalexamroomenable|create_by               |VARCHAR  |128        |Y       |                     |
|mpp_rel_physicalexamroomenable|create_time             |TIMESTAMP|8          |Y       |                     |
|mpp_rel_physicalexamroomenable|update_by               |VARCHAR  |128        |Y       |                     |
|mpp_rel_physicalexamroomenable|update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|mpu_rel_user_role             |uid                     |VARCHAR  |128        |N       |                     |
|mpu_rel_user_role             |user_id                 |VARCHAR  |128        |Y       |                     |
|mpu_rel_user_role             |role_id                 |VARCHAR  |12         |Y       |                     |
|mpu_rel_user_role             |ex_id                   |VARCHAR  |48         |Y       |                     |
|mpu_rel_user_role             |org_id                  |VARCHAR  |144        |Y       |                     |
|mpu_rel_user_role             |examorg_id              |VARCHAR  |144        |Y       |                     |
|mpu_rel_user_role             |create_by               |VARCHAR  |128        |Y       |                     |
|mpu_rel_user_role             |create_time             |TIMESTAMP|8          |Y       |                     |
|mpu_rel_user_role             |update_by               |VARCHAR  |128        |Y       |                     |
|mpu_rel_user_role             |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|mpu_role                      |code                    |VARCHAR  |12         |N       |                     |
|mpu_role                      |role_name               |VARCHAR  |40         |Y       |                     |
|mpu_role                      |role_type               |TINYINT  |1          |Y       |                     |
|mpu_role                      |remark                  |VARCHAR  |400        |Y       |                     |
|mpu_userinfo                  |uid                     |VARCHAR  |128        |N       |                     |
|mpu_userinfo                  |account_name            |VARCHAR  |128        |N       |                     |
|mpu_userinfo                  |order_num               |INT      |4          |N       |0                    |
|mpu_userinfo                  |account_pwd             |VARCHAR  |256        |Y       |                     |
|mpu_userinfo                  |account_type            |CHAR     |1          |N       |                     |
|mpu_userinfo                  |is_default              |CHAR     |1          |N       |'0'                  |
|mpu_userinfo                  |staff_id                |VARCHAR  |128        |Y       |                     |
|mpu_userinfo                  |examstaff_id            |VARCHAR  |128        |Y       |                     |
|mpu_userinfo                  |ex_id                   |VARCHAR  |120        |Y       |                     |
|mpu_userinfo                  |post_code               |VARCHAR  |120        |Y       |                     |
|mpu_userinfo                  |real_name               |VARCHAR  |200        |Y       |                     |
|mpu_userinfo                  |phone                   |VARCHAR  |200        |Y       |                     |
|mpu_userinfo                  |cert_no                 |VARCHAR  |200        |Y       |                     |
|mpu_userinfo                  |gender                  |CHAR     |1          |N       |'0'                  |
|mpu_userinfo                  |birth_date              |VARCHAR  |40         |Y       |                     |
|mpu_userinfo                  |dep_id                  |VARCHAR  |128        |Y       |                     |
|mpu_userinfo                  |org_id                  |VARCHAR  |136        |Y       |                     |
|mpu_userinfo                  |expiration_date         |TIMESTAMP|8          |Y       |                     |
|mpu_userinfo                  |account_status          |CHAR     |1          |N       |'1'                  |
|mpu_userinfo                  |status                  |CHAR     |1          |N       |'A'                  |
|mpu_userinfo                  |create_by               |VARCHAR  |128        |Y       |                     |
|mpu_userinfo                  |create_time             |TIMESTAMP|8          |N       |                     |
|mpu_userinfo                  |update_by               |VARCHAR  |128        |Y       |                     |
|mpu_userinfo                  |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|mpu_userinfo                  |init_instruct           |VARCHAR  |80         |Y       |'123456'             |
|mpu_userinfo                  |user_name               |VARCHAR  |80         |Y       |                     |
|mpw_exam_staffinfo            |uid                     |VARCHAR  |128        |N       |                     |
|mpw_exam_staffinfo            |ex_id                   |VARCHAR  |48         |N       |                     |
|mpw_exam_staffinfo            |staff_id                |VARCHAR  |128        |Y       |                     |
|mpw_exam_staffinfo            |post_code               |VARCHAR  |20         |Y       |                     |
|mpw_exam_staffinfo            |layout_post             |VARCHAR  |20         |Y       |                     |
|mpw_exam_staffinfo            |draw_org                |VARCHAR  |136        |Y       |                     |
|mpw_exam_staffinfo            |send_org                |VARCHAR  |136        |Y       |                     |
|mpw_exam_staffinfo            |drawplan_id             |VARCHAR  |128        |Y       |                     |
|mpw_exam_staffinfo            |is_addto                |CHAR     |1          |N       |'0'                  |
|mpw_exam_staffinfo            |is_addimport            |CHAR     |1          |N       |'0'                  |
|mpw_exam_staffinfo            |is_adjust               |CHAR     |1          |N       |'0'                  |
|mpw_exam_staffinfo            |lock_status             |CHAR     |1          |N       |'0'                  |
|mpw_exam_staffinfo            |is_auto                 |CHAR     |1          |N       |'0'                  |
|mpw_exam_staffinfo            |org_id                  |VARCHAR  |136        |Y       |                     |
|mpw_exam_staffinfo            |fax                     |VARCHAR  |80         |Y       |                     |
|mpw_exam_staffinfo            |home_phone              |VARCHAR  |80         |Y       |                     |
|mpw_exam_staffinfo            |memo                    |VARCHAR  |200        |Y       |                     |
|mpw_exam_staffinfo            |serial_number           |VARCHAR  |12         |Y       |                     |
|mpw_exam_staffinfo            |create_by               |VARCHAR  |128        |Y       |                     |
|mpw_exam_staffinfo            |create_time             |TIMESTAMP|8          |Y       |                     |
|mpw_exam_staffinfo            |update_by               |VARCHAR  |128        |Y       |                     |
|mpw_exam_staffinfo            |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|mpw_exam_staffinfo            |fix_areacode            |VARCHAR  |16         |Y       |                     |
|mpw_exam_staffinfo            |fix_num                 |VARCHAR  |200        |Y       |                     |
|mpw_exam_staffinfo            |duty_phone              |VARCHAR  |400        |Y       |                     |
|mpw_exam_staffinfo            |duty_code               |VARCHAR  |80         |Y       |                     |
|mpw_exam_staffinfo            |phone                   |VARCHAR  |200        |Y       |                     |
|mpw_exam_staffinfo            |home_areacode           |VARCHAR  |16         |Y       |                     |
|mpw_exam_staffinfo            |make_card_time          |TIMESTAMP|8          |Y       |'1970-01-01 00:00:00'|
|mpw_exam_staffinfo            |real_name               |VARCHAR  |200        |N       |''                   |
|mpw_exam_staffinfo            |cert_no                 |VARCHAR  |200        |Y       |                     |
|mpw_exam_staffinfo            |gender                  |CHAR     |1          |N       |'0'                  |
|mpw_exam_staffinfo            |birth_date              |VARCHAR  |40         |Y       |                     |
|mpw_exam_staffinfo            |is_business             |CHAR     |1          |N       |'0'                  |
|mpw_exam_staffinfo            |photo_url               |VARCHAR  |400        |Y       |                     |
|mpw_exam_staffinfo            |sub_logo                |CHAR     |1          |Y       |                     |
|mpw_exam_staffinfo            |prepared_escodes        |VARCHAR  |200        |Y       |                     |
|mpw_layout_examiner           |uid                     |VARCHAR  |128        |N       |                     |
|mpw_layout_examiner           |ex_id                   |VARCHAR  |48         |N       |                     |
|mpw_layout_examiner           |exam_staff_id           |VARCHAR  |128        |Y       |                     |
|mpw_layout_examiner           |es_code                 |VARCHAR  |8          |Y       |                     |
|mpw_layout_examiner           |post_code               |VARCHAR  |20         |Y       |                     |
|mpw_layout_examiner           |group_no                |VARCHAR  |12         |Y       |                     |
|mpw_layout_examiner           |examiner_type           |CHAR     |1          |N       |'1'                  |
|mpw_layout_examiner           |logic_exr_no            |VARCHAR  |200        |Y       |                     |
|mpw_layout_examiner           |loginc_group_no         |VARCHAR  |40         |Y       |                     |
|mpw_layout_examiner           |layout_type             |CHAR     |1          |N       |'1'                  |
|mpw_layout_examiner           |layout_rule             |VARCHAR  |200        |Y       |                     |
|mpw_layout_examiner           |is_layout_rule          |CHAR     |1          |N       |                     |
|mpw_layout_examiner           |un_rule_memo            |VARCHAR  |200        |Y       |                     |
|mpw_layout_examiner           |org_id                  |VARCHAR  |136        |Y       |                     |
|mpw_layout_examiner           |rand_num                |INT      |4          |N       |0                    |
|mpw_layout_examiner           |create_by               |VARCHAR  |128        |Y       |                     |
|mpw_layout_examiner           |create_time             |TIMESTAMP|8          |Y       |                     |
|mpw_layout_examiner           |update_by               |VARCHAR  |128        |Y       |                     |
|mpw_layout_examiner           |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|mpw_layout_examiner           |logic_rand_num          |INT      |4          |Y       |                     |
|mpw_layout_examiner           |pla_id                  |VARCHAR  |200        |Y       |                     |
|mpw_layout_examiner           |physical_group_no       |VARCHAR  |40         |Y       |'00'                 |
|mpw_layout_inspector          |uid                     |VARCHAR  |128        |N       |                     |
|mpw_layout_inspector          |ex_id                   |VARCHAR  |48         |N       |                     |
|mpw_layout_inspector          |exam_staff_id           |VARCHAR  |128        |Y       |                     |
|mpw_layout_inspector          |user_id                 |VARCHAR  |128        |Y       |                     |
|mpw_layout_inspector          |account_name            |VARCHAR  |128        |N       |                     |
|mpw_layout_inspector          |post_code               |VARCHAR  |20         |Y       |                     |
|mpw_layout_inspector          |pt_id                   |VARCHAR  |8          |Y       |                     |
|mpw_layout_inspector          |inspect_org             |VARCHAR  |136        |Y       |                     |
|mpw_layout_inspector          |org_id                  |VARCHAR  |136        |Y       |                     |
|mpw_layout_inspector          |create_by               |VARCHAR  |128        |Y       |                     |
|mpw_layout_inspector          |create_time             |TIMESTAMP|8          |Y       |                     |
|mpw_layout_inspector          |update_by               |VARCHAR  |128        |Y       |                     |
|mpw_layout_inspector          |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|mpw_layout_invigilator        |uid                     |VARCHAR  |128        |N       |                     |
|mpw_layout_invigilator        |ex_id                   |VARCHAR  |48         |N       |                     |
|mpw_layout_invigilator        |exam_staff_id           |VARCHAR  |128        |Y       |                     |
|mpw_layout_invigilator        |es_code                 |VARCHAR  |8          |Y       |                     |
|mpw_layout_invigilator        |post_code               |VARCHAR  |20         |Y       |                     |
|mpw_layout_invigilator        |physical_exr_id         |VARCHAR  |400        |N       |                     |
|mpw_layout_invigilator        |layout_type             |CHAR     |1          |N       |'1'                  |
|mpw_layout_invigilator        |layout_rule             |CHAR     |1          |N       |                     |
|mpw_layout_invigilator        |org_id                  |VARCHAR  |136        |Y       |                     |
|mpw_layout_invigilator        |create_by               |VARCHAR  |128        |Y       |                     |
|mpw_layout_invigilator        |create_time             |TIMESTAMP|8          |Y       |                     |
|mpw_layout_invigilator        |update_by               |VARCHAR  |128        |Y       |                     |
|mpw_layout_invigilator        |update_time             |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|p_abnormal_behavior           |uid                     |VARCHAR  |48         |N       |                     |
|p_abnormal_behavior           |type                    |TINYINT  |1          |N       |                     |
|p_abnormal_behavior           |type_name               |VARCHAR  |120        |Y       |                     |
|p_abnormal_behavior           |code                    |TINYINT  |1          |N       |                     |
|p_abnormal_behavior           |name                    |VARCHAR  |120        |Y       |                     |
|p_abnormal_behavior           |remark                    |VARCHAR  |400        |Y       |                     |
|p_behavior                    |uid                       |VARCHAR  |80         |N       |                     |
|p_behavior                    |et_id                     |VARCHAR  |12         |N       |                     |
|p_behavior                    |behavior_no               |TINYINT  |1          |Y       |                     |
|p_behavior                    |behavior_name             |VARCHAR  |400        |Y       |                     |
|p_behavior                    |detail_no                 |TINYINT  |1          |Y       |                     |
|p_behavior                    |detail_name               |VARCHAR  |400        |Y       |                     |
|p_behavior                    |check_time_type           |TINYINT  |1          |Y       |                     |
|p_behavior                    |check_time_offset         |TINYINT  |1          |Y       |                     |
|p_behavior                    |remark                    |VARCHAR  |800        |Y       |                     |
|p_code                        |uid                       |VARCHAR  |48         |N       |                     |
|p_code                        |type                      |VARCHAR  |24         |N       |                     |
|p_code                        |code                      |VARCHAR  |24         |N       |                     |
|p_code                        |name                      |VARCHAR  |120        |Y       |                     |
|p_code                        |remark                    |VARCHAR  |400        |Y       |                     |
|p_dutycheck                   |uid                       |VARCHAR  |80         |N       |                     |
|p_dutycheck                   |et_id                     |VARCHAR  |48         |Y       |                     |
|p_dutycheck                   |dutycheck_no              |TINYINT  |1          |Y       |                     |
|p_dutycheck                   |dutycheck_name            |VARCHAR  |400        |Y       |                     |
|p_dutycheck                   |detail_no                 |TINYINT  |1          |Y       |                     |
|p_dutycheck                   |detail_name               |VARCHAR  |400        |Y       |                     |
|p_dutycheck                   |check_time_type           |TINYINT  |1          |Y       |                     |
|p_dutycheck                   |check_time_offset         |TINYINT  |1          |Y       |                     |
|p_dutycheck                   |remark                    |VARCHAR  |800        |Y       |                     |
|p_post                        |code                      |VARCHAR  |20         |N       |                     |
|p_post                        |name                      |VARCHAR  |40         |N       |                     |
|p_post                        |type                      |VARCHAR  |8          |N       |                     |
|p_post                        |type_name                 |VARCHAR  |80         |N       |                     |
|p_post                        |is_exschool               |CHAR     |1          |Y       |                     |
|p_post                        |symbol                    |CHAR     |1          |Y       |                     |
|p_post                        |remark                    |VARCHAR  |400        |Y       |                     |
|s_dutycheck                   |uid                       |VARCHAR  |128        |N       |                     |
|s_dutycheck                   |ex_id                     |VARCHAR  |120        |Y       |                     |
|s_dutycheck                   |es_code                   |VARCHAR  |8          |Y       |                     |
|s_dutycheck                   |org_id                    |VARCHAR  |136        |Y       |                     |
|s_dutycheck                   |org_name                  |VARCHAR  |400        |Y       |                     |
|s_dutycheck                   |org_status                |VARCHAR  |4          |Y       |                     |
|s_dutycheck                   |role_id                   |VARCHAR  |12         |Y       |                     |
|s_dutycheck                   |dutycheck_detail_id       |VARCHAR  |128        |Y       |                     |
|s_dutycheck                   |resp_num                  |INT      |4          |Y       |                     |
|s_dutycheck                   |resp_en_num               |INT      |4          |Y       |                     |
|s_dutycheck                   |lt1_en_num                |INT      |4          |Y       |                     |
|s_dutycheck                   |lt3_en_num                |INT      |4          |Y       |                     |
|s_dutycheck                   |gt3_en_num                |INT      |4          |Y       |                     |
|s_dutycheck                   |sum_time                  |INT      |4          |Y       |                     |
|s_dutycheck                   |resp_bj_num               |INT      |4          |Y       |                     |
|s_dutycheck                   |resp_bj_en_num            |INT      |4          |Y       |                     |
|s_dutycheck                   |lt1_bj_en_num             |INT      |4          |Y       |                     |
|s_dutycheck                   |lt3_bj_en_num             |INT      |4          |Y       |                     |
|s_dutycheck                   |gt3_bj_en_num             |INT      |4          |Y       |                     |
|s_dutycheck                   |sum_bj_time               |INT      |4          |Y       |                     |
|s_es_org_datacount            |uid                       |VARCHAR  |128        |N       |                     |
|s_es_org_datacount            |ex_id                     |VARCHAR  |120        |N       |                     |
|s_es_org_datacount            |es_code                   |VARCHAR  |8          |N       |                     |
|s_es_org_datacount            |org_id                    |VARCHAR  |34         |N       |                     |
|s_es_org_datacount            |org_name                  |VARCHAR  |400        |Y       |                     |
|s_es_org_datacount            |lexroom_en_num            |INT      |4          |Y       |0                    |
|s_es_org_datacount            |behavior_comp_normal_num  |INT      |4          |Y       |0                    |
|s_es_org_datacount            |behavior_comp_fault_num   |INT      |4          |Y       |0                    |
|s_es_org_datacount            |behavior_incomp_normal_num|INT      |4          |Y       |0                    |
|s_es_org_datacount            |behavior_incomp_fault_num |INT      |4          |Y       |0                    |
|s_randomcheck                 |uid                       |VARCHAR  |128        |N       |                     |
|s_randomcheck                 |ex_id                     |VARCHAR  |120        |Y       |                     |
|s_randomcheck                 |es_code                   |VARCHAR  |8          |Y       |                     |
|s_randomcheck                 |org_id                    |VARCHAR  |136        |Y       |                     |
|s_randomcheck                 |org_name                  |VARCHAR  |400        |Y       |                     |
|s_randomcheck                 |org_status                |VARCHAR  |4          |Y       |                     |
|s_randomcheck                 |role_id                   |VARCHAR  |12         |Y       |                     |
|s_randomcheck                 |random_detail_id          |VARCHAR  |128        |Y       |                     |
|s_randomcheck                 |resp_num                  |INT      |4          |Y       |                     |
|s_randomcheck                 |resp_en_num               |INT      |4          |Y       |                     |
|s_randomcheck                 |lt1_en_num                |INT      |4          |Y       |                     |
|s_randomcheck                 |lt3_en_num                |INT      |4          |Y       |                     |
|s_randomcheck                 |gt3_en_num                |INT      |4          |Y       |                     |
|s_randomcheck                 |sum_time                  |INT      |4          |Y       |                     |
|s_randomcheck                 |resp_bj_num               |INT      |4          |Y       |                     |
|s_randomcheck                 |resp_bj_en_num            |INT      |4          |Y       |                     |
|s_randomcheck                 |lt1_bj_en_num             |INT      |4          |Y       |                     |
|s_randomcheck                 |lt3_bj_en_num             |INT      |4          |Y       |                     |
|s_randomcheck                 |gt3_bj_en_num             |INT      |4          |Y       |                     |
|s_randomcheck                 |sum_bj_time               |INT      |4          |Y       |                     |
|vis_behavior                  |uid                       |VARCHAR  |128        |N       |                     |
|vis_behavior                  |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_behavior                  |behavior_name             |VARCHAR  |400        |Y       |                     |
|vis_behavior                  |remark                    |VARCHAR  |800        |Y       |                     |
|vis_behavior                  |status                    |CHAR     |1          |N       |                     |
|vis_behavior                  |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_behavior                  |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_behavior                  |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_behavior                  |update_time               |TIMESTAMP|8          |N       |                     |
|vis_behavior_check            |uid                       |VARCHAR  |128        |N       |                     |
|vis_behavior_check            |ex_id                     |VARCHAR  |30         |N       |                     |
|vis_behavior_check            |es_code                   |VARCHAR  |2          |N       |                     |
|vis_behavior_check            |org_id                    |VARCHAR  |136        |Y       |                     |
|vis_behavior_check            |logic_exr_no              |VARCHAR  |50         |N       |                     |
|vis_behavior_check            |pla_id                    |VARCHAR  |200        |Y       |                     |
|vis_behavior_check            |behavior_id               |VARCHAR  |128        |Y       |                     |
|vis_behavior_check            |behavior_detail_id        |VARCHAR  |128        |Y       |                     |
|vis_behavior_check            |check_time                |TIMESTAMP|8          |Y       |                     |
|vis_behavior_check            |check_result              |TINYINT  |1          |Y       |                     |
|vis_behavior_check            |remark                    |VARCHAR  |800        |Y       |                     |
|vis_behavior_check            |user_id                   |VARCHAR  |200        |N       |                     |
|vis_behavior_check            |role_id                   |VARCHAR  |12         |Y       |                     |
|vis_behavior_check            |report_time               |TIMESTAMP|8          |Y       |                     |
|vis_behavior_check            |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_behavior_check            |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_behavior_check            |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_behavior_check            |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_behavior_detail           |uid                       |VARCHAR  |128        |N       |                     |
|vis_behavior_detail           |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_behavior_detail           |behavior_id               |VARCHAR  |128        |N       |                     |
|vis_behavior_detail           |name                      |VARCHAR  |400        |Y       |                     |
|vis_behavior_detail           |check_time_type           |TINYINT  |1          |Y       |                     |
|vis_behavior_detail           |check_time_offset         |TINYINT  |1          |Y       |                     |
|vis_behavior_detail           |remark                    |VARCHAR  |800        |Y       |                     |
|vis_behavior_detail           |status                    |CHAR     |1          |N       |                     |
|vis_behavior_detail           |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_behavior_detail           |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_behavior_detail           |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_behavior_detail           |update_time               |TIMESTAMP|8          |Y       |                     |
|vis_code                      |uid                       |VARCHAR  |60         |N       |                     |
|vis_code                      |type                      |VARCHAR  |32         |N       |                     |
|vis_code                      |code                      |VARCHAR  |24         |N       |                     |
|vis_code                      |name                      |VARCHAR  |120        |Y       |                     |
|vis_code                      |remark                    |VARCHAR  |400        |Y       |                     |
|vis_config                    |uid                       |VARCHAR  |128        |N       |                     |
|vis_config                    |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_config                    |behavior_status           |TINYINT  |1          |Y       |0                    |
|vis_config                    |behavior_roles            |VARCHAR  |80         |Y       |                     |
|vis_config                    |dutycheck_status          |TINYINT  |1          |Y       |0                    |
|vis_config                    |dutycheck_roles           |VARCHAR  |80         |Y       |                     |
|vis_config                    |randomcheck_interval      |TINYINT  |1          |Y       |5                    |
|vis_config                    |inspect_poll_switch       |TINYINT  |1          |Y       |                     |
|vis_config                    |examinee_open_time        |TIMESTAMP|8          |Y       |                     |
|vis_config                    |examiner_open_time        |TIMESTAMP|8          |Y       |                     |
|vis_config                    |dutycheck_resp_method     |TINYINT  |1          |Y       |2                    |
|vis_config                    |dutycheck_resp_duration   |TINYINT  |1          |Y       |5                    |
|vis_config                    |behavior_resp_mehtod      |TINYINT  |1          |Y       |2                    |
|vis_config                    |behavior_resp_duration    |TINYINT  |1          |Y       |20                   |
|vis_config                    |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_config                    |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_config                    |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_config                    |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_config                    |behavior_publish_time     |TIMESTAMP|8          |Y       |                     |
|vis_config                    |dutycheck_publish_time    |TIMESTAMP|8          |Y       |                     |
|vis_dutycheck                 |uid                       |VARCHAR  |128        |N       |                     |
|vis_dutycheck                 |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_dutycheck                 |detycheck_name            |VARCHAR  |400        |Y       |                     |
|vis_dutycheck                 |remark                    |VARCHAR  |800        |Y       |                     |
|vis_dutycheck                 |status                    |CHAR     |1          |N       |                     |
|vis_dutycheck                 |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_dutycheck                 |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_dutycheck                 |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_dutycheck                 |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_dutycheck_detail          |uid                       |VARCHAR  |128        |N       |                     |
|vis_dutycheck_detail          |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_dutycheck_detail          |dutycheck_id              |VARCHAR  |128        |N       |                     |
|vis_dutycheck_detail          |dutycheck_name            |VARCHAR  |400        |Y       |                     |
|vis_dutycheck_detail          |check_time_type           |TINYINT  |1          |Y       |                     |
|vis_dutycheck_detail          |check_time_offset         |SMALLINT |2          |Y       |                     |
|vis_dutycheck_detail          |remark                    |VARCHAR  |800        |Y       |                     |
|vis_dutycheck_detail          |status                    |CHAR     |1          |N       |                     |
|vis_dutycheck_detail          |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_dutycheck_detail          |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_dutycheck_detail          |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_dutycheck_detail          |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_dutycheck_response        |uid                       |VARCHAR  |128        |N       |                     |
|vis_dutycheck_response        |ex_id                     |VARCHAR  |30         |N       |                     |
|vis_dutycheck_response        |es_code                   |VARCHAR  |2          |N       |                     |
|vis_dutycheck_response        |org_id                    |VARCHAR  |136        |Y       |                     |
|vis_dutycheck_response        |dutycheck_id              |VARCHAR  |128        |Y       |                     |
|vis_dutycheck_response        |dutycheck_detail_id       |VARCHAR  |128        |Y       |                     |
|vis_dutycheck_response        |response_result           |TINYINT  |1          |Y       |                     |
|vis_dutycheck_response        |user_id                   |VARCHAR  |200        |N       |                     |
|vis_dutycheck_response        |role_id                   |VARCHAR  |12         |Y       |                     |
|vis_dutycheck_response        |report_time               |TIMESTAMP|8          |Y       |                     |
|vis_dutycheck_response        |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_dutycheck_response        |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_dutycheck_response        |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_dutycheck_response        |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_examinee_check_in         |uid                       |VARCHAR  |128        |N       |                     |
|vis_examinee_check_in         |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_examinee_check_in         |es_code                   |VARCHAR  |8          |N       |                     |
|vis_examinee_check_in         |org_id                    |VARCHAR  |136        |N       |                     |
|vis_examinee_check_in         |logic_exr_no              |VARCHAR  |200        |N       |                     |
|vis_examinee_check_in         |pla_id                    |VARCHAR  |200        |Y       |                     |
|vis_examinee_check_in         |check_in_result           |TINYINT  |1          |N       |1                    |
|vis_examinee_check_in         |name                      |VARCHAR  |200        |Y       |                     |
|vis_examinee_check_in         |gender_id                 |VARCHAR  |20         |Y       |                     |
|vis_examinee_check_in         |zkzh                      |VARCHAR  |200        |N       |                     |
|vis_examinee_check_in         |seat_no                   |TINYINT  |1          |Y       |                     |
|vis_examinee_check_in         |kskmdm                    |VARCHAR  |40         |Y       |                     |
|vis_examinee_check_in         |kskmmc                    |VARCHAR  |200        |Y       |                     |
|vis_examinee_check_in         |cert_no                   |VARCHAR  |200        |Y       |                     |
|vis_examinee_check_in         |photo_url                 |VARCHAR  |400        |Y       |                     |
|vis_examinee_check_in         |onsite_photo_url          |VARCHAR  |400        |Y       |                     |
|vis_examinee_check_in         |user_id                   |VARCHAR  |128        |Y       |                     |
|vis_examinee_check_in         |role_id                   |VARCHAR  |12         |Y       |                     |
|vis_examinee_check_in         |report_time               |TIMESTAMP|8          |Y       |                     |
|vis_examinee_check_in         |remark                    |VARCHAR  |400        |Y       |                     |
|vis_examinee_check_in         |status                    |CHAR     |1          |N       |'A'                  |
|vis_examinee_check_in         |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_examinee_check_in         |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_examinee_check_in         |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_examinee_check_in         |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_examroom_abbehavior       |uid                       |VARCHAR  |128        |N       |                     |
|vis_examroom_abbehavior       |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_examroom_abbehavior       |es_code                   |VARCHAR  |8          |N       |                     |
|vis_examroom_abbehavior       |org_id                    |VARCHAR  |136        |N       |                     |
|vis_examroom_abbehavior       |logic_exr_no              |VARCHAR  |200        |N       |                     |
|vis_examroom_abbehavior       |pla_id                    |VARCHAR  |200        |Y       |                     |
|vis_examroom_abbehavior       |abbeh_type                |TINYINT  |1          |N       |                     |
|vis_examroom_abbehavior       |abbeh_code                |TINYINT  |1          |N       |                     |
|vis_examroom_abbehavior       |abbeh_time                |TIMESTAMP|8          |Y       |                     |
|vis_examroom_abbehavior       |report_way                |TINYINT  |1          |Y       |2                    |
|vis_examroom_abbehavior       |user_id                   |VARCHAR  |128        |Y       |                     |
|vis_examroom_abbehavior       |role_id                   |VARCHAR  |12         |Y       |                     |
|vis_examroom_abbehavior       |report_time               |TIMESTAMP|8          |Y       |                     |
|vis_examroom_abbehavior       |remark                    |VARCHAR  |400        |Y       |                     |
|vis_examroom_abbehavior       |status                    |CHAR     |1          |N       |'A'                  |
|vis_examroom_abbehavior       |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_abbehavior       |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_examroom_abbehavior       |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_abbehavior       |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_examroom_abbehavior_file  |uid                       |VARCHAR  |128        |N       |                     |
|vis_examroom_abbehavior_file  |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_examroom_abbehavior_file  |abbeh_id                  |VARCHAR  |128        |N       |                     |
|vis_examroom_abbehavior_file  |file_type                 |TINYINT  |1          |Y       |                     |
|vis_examroom_abbehavior_file  |file_url                  |VARCHAR  |800        |Y       |                     |
|vis_examroom_abbehavior_file  |file_name                 |VARCHAR  |400        |Y       |                     |
|vis_examroom_abbehavior_file  |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_abbehavior_file  |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_examroom_abbehavior_file  |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_abbehavior_file  |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_examroom_absence          |uid                       |VARCHAR  |128        |N       |                     |
|vis_examroom_absence          |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_examroom_absence          |es_code                   |VARCHAR  |8          |N       |                     |
|vis_examroom_absence          |org_id                    |VARCHAR  |136        |N       |                     |
|vis_examroom_absence          |logic_exr_no              |VARCHAR  |200        |N       |                     |
|vis_examroom_absence          |pla_id                    |VARCHAR  |200        |Y       |                     |
|vis_examroom_absence          |absence_num               |NUMBER   |22         |Y       |                     |
|vis_examroom_absence          |user_id                   |VARCHAR  |128        |Y       |                     |
|vis_examroom_absence          |role_id                   |VARCHAR  |12         |Y       |                     |
|vis_examroom_absence          |report_time               |TIMESTAMP|8          |Y       |                     |
|vis_examroom_absence          |remark                    |VARCHAR  |400        |Y       |                     |
|vis_examroom_absence          |status                    |CHAR     |1          |N       |'A'                  |
|vis_examroom_absence          |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_absence          |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_examroom_absence          |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_absence          |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_examroom_behavior_status  |uid                       |VARCHAR  |128        |N       |                     |
|vis_examroom_behavior_status  |ex_id                     |VARCHAR  |48         |N       |                     |
|vis_examroom_behavior_status  |es_code                   |VARCHAR  |8          |N       |                     |
|vis_examroom_behavior_status  |org_id                    |VARCHAR  |136        |Y       |                     |
|vis_examroom_behavior_status  |logic_exr_no              |VARCHAR  |50         |N       |                     |
|vis_examroom_behavior_status  |pla_id                    |VARCHAR  |200        |Y       |                     |
|vis_examroom_behavior_status  |behavior_id               |VARCHAR  |128        |N       |                     |
|vis_examroom_behavior_status  |behavior_num              |SMALLINT |2          |Y       |                     |
|vis_examroom_behavior_status  |behavior_detail_normal    |SMALLINT |2          |Y       |                     |
|vis_examroom_behavior_status  |behavior_detail_fault     |SMALLINT |2          |Y       |                     |
|vis_examroom_behavior_status  |user_id                   |VARCHAR  |200        |N       |                     |
|vis_examroom_behavior_status  |role_id                   |VARCHAR  |12         |Y       |                     |
|vis_examroom_behavior_status  |last_report_time          |TIMESTAMP|8          |Y       |                     |
|vis_examroom_behavior_status  |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_behavior_status  |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_examroom_behavior_status  |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_behavior_status  |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_examroom_camera_check     |uid                       |VARCHAR  |128        |N       |                     |
|vis_examroom_camera_check     |ex_id                     |VARCHAR  |48         |N       |                     |
|vis_examroom_camera_check     |es_code                   |VARCHAR  |8          |N       |                     |
|vis_examroom_camera_check     |org_id                    |VARCHAR  |136        |N       |                     |
|vis_examroom_camera_check     |logic_exr_no              |VARCHAR  |200        |N       |                     |
|vis_examroom_camera_check     |pla_id                    |VARCHAR  |400        |Y       |                     |
|vis_examroom_camera_check     |device_id                 |VARCHAR  |40         |Y       |                     |
|vis_examroom_camera_check     |check_result              |VARCHAR  |40         |N       |1                    |
|vis_examroom_camera_check     |remark                    |VARCHAR  |400        |N       |                     |
|vis_examroom_camera_check     |user_id                   |VARCHAR  |200        |Y       |                     |
|vis_examroom_camera_check     |role_id                   |VARCHAR  |12         |Y       |                     |
|vis_examroom_camera_check     |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_camera_check     |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_examroom_camera_check     |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_camera_check     |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_examroom_camera_file      |uid                       |VARCHAR  |128        |N       |                     |
|vis_examroom_camera_file      |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_examroom_camera_file      |es_code                   |VARCHAR  |8          |N       |                     |
|vis_examroom_camera_file      |org_id                    |VARCHAR  |136        |N       |                     |
|vis_examroom_camera_file      |logic_exr_no              |VARCHAR  |200        |N       |                     |
|vis_examroom_camera_file      |pla_id                    |VARCHAR  |400        |Y       |                     |
|vis_examroom_camera_file      |device_id                 |VARCHAR  |40         |Y       |                     |
|vis_examroom_camera_file      |file_type                 |TINYINT  |1          |Y       |                     |
|vis_examroom_camera_file      |file_url                  |VARCHAR  |800        |Y       |                     |
|vis_examroom_camera_file      |file_name                 |VARCHAR  |400        |Y       |                     |
|vis_examroom_camera_file      |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_camera_file      |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_examroom_camera_file      |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_camera_file      |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_examroom_camera_status    |uid                       |VARCHAR  |128        |N       |                     |
|vis_examroom_camera_status    |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_examroom_camera_status    |es_code                   |VARCHAR  |8          |N       |                     |
|vis_examroom_camera_status    |org_id                    |VARCHAR  |136        |N       |                     |
|vis_examroom_camera_status    |logic_exr_no              |VARCHAR  |200        |N       |                     |
|vis_examroom_camera_status    |pla_id                    |VARCHAR  |200        |Y       |                     |
|vis_examroom_camera_status    |camera_status             |TINYINT  |1          |N       |0                    |
|vis_examroom_camera_status    |user_id                   |VARCHAR  |128        |Y       |                     |
|vis_examroom_camera_status    |role_id                   |VARCHAR  |12         |Y       |                     |
|vis_examroom_camera_status    |report_time               |TIMESTAMP|8          |Y       |                     |
|vis_examroom_camera_status    |remark                    |VARCHAR  |400        |Y       |                     |
|vis_examroom_camera_status    |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_camera_status    |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_examroom_camera_status    |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_examroom_camera_status    |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_instruct                  |uid                       |VARCHAR  |128        |N       |                     |
|vis_instruct                  |ex_id                     |VARCHAR  |48         |N       |                     |
|vis_instruct                  |instruct_title            |VARCHAR  |200        |N       |                     |
|vis_instruct                  |instruct_content          |VARCHAR  |800        |N       |                     |
|vis_instruct                  |send_user                 |VARCHAR  |128        |Y       |                     |
|vis_instruct                  |send_org_id               |VARCHAR  |200        |Y       |                     |
|vis_instruct                  |remark                    |VARCHAR  |400        |Y       |                     |
|vis_instruct                  |status                    |CHAR     |1          |N       |'A'                  |
|vis_instruct                  |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_instruct                  |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_instruct                  |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_instruct                  |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_randomcheck_detail        |uid                       |VARCHAR  |128        |N       |                     |
|vis_randomcheck_detail        |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_randomcheck_detail        |es_code                   |VARCHAR  |8          |Y       |                     |
|vis_randomcheck_detail        |randomcheck_name          |VARCHAR  |400        |Y       |                     |
|vis_randomcheck_detail        |randomcheck_time          |TIMESTAMP|8          |Y       |                     |
|vis_randomcheck_detail        |randomcheck_roles         |VARCHAR  |80         |Y       |                     |
|vis_randomcheck_detail        |org_id                    |VARCHAR  |136        |Y       |                     |
|vis_randomcheck_detail        |remark                    |VARCHAR  |800        |Y       |                     |
|vis_randomcheck_detail        |status                    |CHAR     |1          |N       |                     |
|vis_randomcheck_detail        |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_randomcheck_detail        |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_randomcheck_detail        |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_randomcheck_detail        |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_randomcheck_response      |uid                       |VARCHAR  |128        |N       |                     |
|vis_randomcheck_response      |ex_id                     |VARCHAR  |30         |N       |                     |
|vis_randomcheck_response      |es_code                   |VARCHAR  |2          |N       |                     |
|vis_randomcheck_response      |org_id                    |VARCHAR  |136        |Y       |                     |
|vis_randomcheck_response      |randomcheck_detail_id     |VARCHAR  |128        |Y       |                     |
|vis_randomcheck_response      |response_result           |TINYINT  |1          |Y       |                     |
|vis_randomcheck_response      |user_id                   |VARCHAR  |200        |N       |                     |
|vis_randomcheck_response      |role_id                   |VARCHAR  |12         |Y       |                     |
|vis_randomcheck_response      |report_time               |TIMESTAMP|8          |Y       |                     |
|vis_randomcheck_response      |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_randomcheck_response      |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_randomcheck_response      |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_randomcheck_response      |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_rel_abbehavior_examinee   |uid                       |VARCHAR  |128        |N       |                     |
|vis_rel_abbehavior_examinee   |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_rel_abbehavior_examinee   |abbeh_id                  |VARCHAR  |128        |N       |                     |
|vis_rel_abbehavior_examinee   |name                      |VARCHAR  |200        |Y       |                     |
|vis_rel_abbehavior_examinee   |gender_id                 |VARCHAR  |20         |Y       |                     |
|vis_rel_abbehavior_examinee   |zkzh                      |VARCHAR  |200        |N       |                     |
|vis_rel_abbehavior_examinee   |seat_no                   |TINYINT  |1          |Y       |                     |
|vis_rel_abbehavior_examinee   |kskmdm                    |VARCHAR  |40         |Y       |                     |
|vis_rel_abbehavior_examinee   |kskmmc                    |VARCHAR  |200        |Y       |                     |
|vis_rel_abbehavior_examinee   |cert_no                   |VARCHAR  |200        |Y       |                     |
|vis_rel_abbehavior_examinee   |photo_url                 |VARCHAR  |400        |Y       |                     |
|vis_rel_abbehavior_examinee   |remark                    |VARCHAR  |400        |Y       |                     |
|vis_rel_abbehavior_examinee   |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_abbehavior_examinee   |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_rel_abbehavior_examinee   |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_abbehavior_examinee   |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_rel_abbehavior_examineer  |uid                       |VARCHAR  |128        |N       |                     |
|vis_rel_abbehavior_examineer  |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_rel_abbehavior_examineer  |abbeh_id                  |VARCHAR  |128        |N       |                     |
|vis_rel_abbehavior_examineer  |exam_staff_id             |VARCHAR  |128        |N       |                     |
|vis_rel_abbehavior_examineer  |remark                    |VARCHAR  |400        |Y       |                     |
|vis_rel_abbehavior_examineer  |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_abbehavior_examineer  |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_rel_abbehavior_examineer  |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_abbehavior_examineer  |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_rel_absence_examinee      |uid                       |VARCHAR  |128        |N       |                     |
|vis_rel_absence_examinee      |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_rel_absence_examinee      |absence_id                |VARCHAR  |128        |N       |                     |
|vis_rel_absence_examinee      |name                      |VARCHAR  |200        |Y       |                     |
|vis_rel_absence_examinee      |gender_id                 |VARCHAR  |20         |Y       |                     |
|vis_rel_absence_examinee      |zkzh                      |VARCHAR  |200        |N       |                     |
|vis_rel_absence_examinee      |seat_no                   |TINYINT  |1          |Y       |                     |
|vis_rel_absence_examinee      |kskmdm                    |VARCHAR  |40         |Y       |                     |
|vis_rel_absence_examinee      |kskmmc                    |VARCHAR  |200        |Y       |                     |
|vis_rel_absence_examinee      |cert_no                   |VARCHAR  |200        |Y       |                     |
|vis_rel_absence_examinee      |photo_url                 |VARCHAR  |400        |Y       |                     |
|vis_rel_absence_examinee      |remark                    |VARCHAR  |400        |Y       |                     |
|vis_rel_absence_examinee      |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_absence_examinee      |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_rel_absence_examinee      |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_absence_examinee      |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_rel_behavior_subject      |uid                       |VARCHAR  |128        |N       |                     |
|vis_rel_behavior_subject      |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_rel_behavior_subject      |behavior_id               |VARCHAR  |128        |N       |                     |
|vis_rel_behavior_subject      |es_code                   |VARCHAR  |8          |N       |                     |
|vis_rel_behavior_subject      |subject_code              |VARCHAR  |40         |Y       |                     |
|vis_rel_behavior_subject      |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_behavior_subject      |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_rel_behavior_subject      |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_behavior_subject      |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_rel_dutycheck_escode      |uid                       |VARCHAR  |128        |N       |                     |
|vis_rel_dutycheck_escode      |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_rel_dutycheck_escode      |dutycheck_id              |VARCHAR  |128        |N       |                     |
|vis_rel_dutycheck_escode      |es_code                   |VARCHAR  |8          |N       |                     |
|vis_rel_dutycheck_escode      |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_dutycheck_escode      |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_rel_dutycheck_escode      |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_dutycheck_escode      |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
|vis_rel_instruct_user         |uid                       |VARCHAR  |128        |N       |                     |
|vis_rel_instruct_user         |ex_id                     |VARCHAR  |120        |N       |                     |
|vis_rel_instruct_user         |instruct_id               |VARCHAR  |128        |N       |                     |
|vis_rel_instruct_user         |receive_user              |VARCHAR  |128        |N       |                     |
|vis_rel_instruct_user         |receive_role              |VARCHAR  |12         |Y       |                     |
|vis_rel_instruct_user         |receive_org_id            |VARCHAR  |136        |Y       |                     |
|vis_rel_instruct_user         |read_status               |TINYINT  |1          |Y       |0                    |
|vis_rel_instruct_user         |read_time                 |TIMESTAMP|8          |Y       |                     |
|vis_rel_instruct_user         |remark                    |VARCHAR  |400        |Y       |                     |
|vis_rel_instruct_user         |create_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_instruct_user         |create_time               |TIMESTAMP|8          |Y       |                     |
|vis_rel_instruct_user         |update_by                 |VARCHAR  |128        |Y       |                     |
|vis_rel_instruct_user         |update_time               |TIMESTAMP|8          |N       |CURRENT_TIMESTAMP()  |
