[DM_DATABASE]
# DM数据库配置
host = localhost
port = 5236
user = SYSDBA
password = SYSDBA
database = DMSERVER
charset = utf8

[MYSQL_DATABASE]
# MySQL数据库配置
host = localhost
port = 3306
user = root
password = password
database = sync_db
charset = utf8mb4

[SYNC_SETTINGS]
# 同步设置
batch_size = 1000
mysql_batch_size = 500
# 是否在同步前清空MySQL表 (true/false)
clear_target_tables = true
# 日志级别 (DEBUG/INFO/WARNING/ERROR)
log_level = INFO
# 超时设置（秒）
connection_timeout = 30
# 重试次数
max_retries = 3

[FILES]
# 文件路径配置
table_columns_file = USER_TAB_COLUMNS_202507291738.txt
table_indexes_file = USER_IND_COLUMNS_202507300903.txt
log_file = dm_to_mysql_sync.log

[TABLES]
# 表同步配置
# 如果指定了特定表，只同步这些表，否则同步所有表
# 格式: table1,table2,table3
# 留空表示同步所有表
specific_tables = 

# 排除的表（不进行同步）
exclude_tables = 

# 表名映射（如果源表名和目标表名不同）
# 格式: source_table:target_table
table_mapping = 
