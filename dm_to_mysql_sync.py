#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DM数据库到MySQL数据库同步脚本
从文本文件读取表名称，循环读取DM数据并写入MySQL
创建日期: 2024-07-30
"""

import re
import sys
import logging
import traceback
import configparser
from typing import List, Dict, Set, Optional
from datetime import datetime
import pymysql
import dmPython

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dm_to_mysql_sync.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """数据库配置类"""
    
    # DM数据库配置
    DM_CONFIG = {
        'host': 'localhost',
        'port': 5236,
        'user': 'SYSDBA',
        'password': 'SYSDBA',
        'database': 'DMSERVER',
        'charset': 'utf8'
    }
    
    # MySQL数据库配置
    MYSQL_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'password',
        'database': 'sync_db',
        'charset': 'utf8mb4'
    }

class TableInfoParser:
    """表信息解析器"""
    
    @staticmethod
    def parse_table_columns_file(file_path: str) -> Dict[str, List[Dict]]:
        """
        解析表列信息文件
        返回格式: {table_name: [{'column_name': str, 'data_type': str, ...}, ...]}
        """
        tables_info = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 跳过表头
            for line in lines[2:]:  # 跳过前两行（表头和分隔符）
                line = line.strip()
                if not line:
                    continue
                
                # 解析表格行，使用正则表达式处理管道分隔的数据
                parts = [part.strip() for part in line.split('|') if part.strip()]
                if len(parts) >= 6:
                    table_name = parts[0]
                    column_info = {
                        'column_name': parts[1],
                        'data_type': parts[2],
                        'data_length': parts[3],
                        'nullable': parts[4],
                        'data_default': parts[5] if len(parts) > 5 else ''
                    }
                    
                    if table_name not in tables_info:
                        tables_info[table_name] = []
                    tables_info[table_name].append(column_info)
                    
        except Exception as e:
            logger.error(f"解析表列信息文件失败: {e}")
            raise
            
        return tables_info
    
    @staticmethod
    def parse_table_indexes_file(file_path: str) -> Dict[str, Set[str]]:
        """
        解析表索引信息文件
        返回格式: {table_name: {column1, column2, ...}}
        """
        tables_indexes = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 跳过表头
            for line in lines[2:]:  # 跳过前两行
                line = line.strip()
                if not line:
                    continue
                
                parts = [part.strip() for part in line.split('|') if part.strip()]
                if len(parts) >= 3:
                    table_name = parts[0]
                    column_name = parts[1]
                    
                    if table_name not in tables_indexes:
                        tables_indexes[table_name] = set()
                    tables_indexes[table_name].add(column_name)
                    
        except Exception as e:
            logger.error(f"解析表索引信息文件失败: {e}")
            raise
            
        return tables_indexes

class DatabaseSyncer:
    """数据库同步器"""
    
    def __init__(self):
        self.dm_conn = None
        self.mysql_conn = None
        
    def connect_databases(self):
        """连接数据库"""
        try:
            # 连接DM数据库
            self.dm_conn = dmPython.connect(
                user=DatabaseConfig.DM_CONFIG['user'],
                password=DatabaseConfig.DM_CONFIG['password'],
                server=DatabaseConfig.DM_CONFIG['host'],
                port=DatabaseConfig.DM_CONFIG['port']
            )
            logger.info("DM数据库连接成功")
            
            # 连接MySQL数据库
            self.mysql_conn = pymysql.connect(**DatabaseConfig.MYSQL_CONFIG)
            logger.info("MySQL数据库连接成功")
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def close_connections(self):
        """关闭数据库连接"""
        if self.dm_conn:
            self.dm_conn.close()
            logger.info("DM数据库连接已关闭")
        if self.mysql_conn:
            self.mysql_conn.close()
            logger.info("MySQL数据库连接已关闭")
    
    def get_table_data(self, table_name: str, batch_size: int = 1000) -> List[Dict]:
        """从DM数据库获取表数据"""
        try:
            cursor = self.dm_conn.cursor()
            
            # 获取表的列信息
            cursor.execute(f"SELECT * FROM {table_name} WHERE ROWNUM <= 1")
            columns = [desc[0].lower() for desc in cursor.description]
            
            # 分批获取数据
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_count = cursor.fetchone()[0]
            logger.info(f"表 {table_name} 总记录数: {total_count}")
            
            all_data = []
            offset = 0
            
            while offset < total_count:
                # DM数据库分页查询
                query = f"""
                SELECT * FROM (
                    SELECT ROWNUM as rn, t.* FROM {table_name} t 
                    WHERE ROWNUM <= {offset + batch_size}
                ) WHERE rn > {offset}
                """
                
                cursor.execute(query)
                batch_data = cursor.fetchall()
                
                for row in batch_data:
                    # 跳过ROWNUM列
                    row_data = row[1:] if len(row) > len(columns) else row
                    row_dict = dict(zip(columns, row_data))
                    all_data.append(row_dict)
                
                offset += batch_size
                logger.info(f"已获取 {table_name} 数据: {min(offset, total_count)}/{total_count}")
            
            cursor.close()
            return all_data
            
        except Exception as e:
            logger.error(f"获取表 {table_name} 数据失败: {e}")
            raise
    
    def insert_mysql_data(self, table_name: str, data: List[Dict], batch_size: int = 500):
        """向MySQL插入数据"""
        if not data:
            logger.warning(f"表 {table_name} 没有数据需要插入")
            return
        
        try:
            cursor = self.mysql_conn.cursor()
            
            # 清空目标表
            cursor.execute(f"DELETE FROM {table_name}")
            logger.info(f"已清空MySQL表 {table_name}")
            
            # 准备插入语句
            columns = list(data[0].keys())
            placeholders = ', '.join(['%s'] * len(columns))
            insert_sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            # 分批插入数据
            total_rows = len(data)
            inserted_rows = 0
            
            for i in range(0, total_rows, batch_size):
                batch = data[i:i + batch_size]
                batch_values = []
                
                for row in batch:
                    values = []
                    for col in columns:
                        value = row.get(col)
                        # 处理特殊值
                        if value is None:
                            values.append(None)
                        elif isinstance(value, datetime):
                            values.append(value.strftime('%Y-%m-%d %H:%M:%S'))
                        else:
                            values.append(str(value) if value is not None else None)
                    batch_values.append(values)
                
                cursor.executemany(insert_sql, batch_values)
                self.mysql_conn.commit()
                
                inserted_rows += len(batch)
                logger.info(f"表 {table_name} 已插入: {inserted_rows}/{total_rows}")
            
            cursor.close()
            logger.info(f"表 {table_name} 数据同步完成，共插入 {inserted_rows} 条记录")
            
        except Exception as e:
            logger.error(f"向MySQL插入数据失败 {table_name}: {e}")
            self.mysql_conn.rollback()
            raise
    
    def sync_table(self, table_name: str):
        """同步单个表"""
        logger.info(f"开始同步表: {table_name}")
        
        try:
            # 从DM获取数据
            data = self.get_table_data(table_name)
            
            # 插入到MySQL
            self.insert_mysql_data(table_name, data)
            
            logger.info(f"表 {table_name} 同步完成")
            
        except Exception as e:
            logger.error(f"表 {table_name} 同步失败: {e}")
            logger.error(traceback.format_exc())
            raise

def main():
    """主函数"""
    logger.info("开始DM到MySQL数据同步")
    
    # 解析表信息文件
    parser = TableInfoParser()
    
    try:
        # 解析表列信息
        tables_columns = parser.parse_table_columns_file('USER_TAB_COLUMNS_202507291738.txt')
        logger.info(f"解析到 {len(tables_columns)} 个表的列信息")
        
        # 解析表索引信息
        tables_indexes = parser.parse_table_indexes_file('USER_IND_COLUMNS_202507300903.txt')
        logger.info(f"解析到 {len(tables_indexes)} 个表的索引信息")
        
        # 获取所有需要同步的表名
        all_tables = set(tables_columns.keys()) | set(tables_indexes.keys())
        logger.info(f"需要同步的表总数: {len(all_tables)}")
        
        # 创建同步器
        syncer = DatabaseSyncer()
        syncer.connect_databases()
        
        # 同步每个表
        success_count = 0
        failed_tables = []
        
        for table_name in sorted(all_tables):
            try:
                syncer.sync_table(table_name)
                success_count += 1
            except Exception as e:
                failed_tables.append(table_name)
                logger.error(f"表 {table_name} 同步失败: {e}")
                continue
        
        # 关闭连接
        syncer.close_connections()
        
        # 输出同步结果
        logger.info(f"数据同步完成！")
        logger.info(f"成功同步: {success_count} 个表")
        logger.info(f"同步失败: {len(failed_tables)} 个表")
        
        if failed_tables:
            logger.warning(f"失败的表: {', '.join(failed_tables)}")
        
    except Exception as e:
        logger.error(f"同步过程发生错误: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
