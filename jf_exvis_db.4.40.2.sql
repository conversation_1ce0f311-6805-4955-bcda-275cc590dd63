/*
 Navicat Premium Dump SQL

 Source Server         : GR-Mac-Mysql8.0_1
 Source Server Type    : MySQL
 Source Server Version : 80036 (8.0.36)
 Source Host           : localhost:3306
 Source Schema         : jf_exvis_db.4.40.2

 Target Server Type    : MySQL
 Target Server Version : 80036 (8.0.36)
 File Encoding         : 65001

 Date: 30/07/2025 09:04:09
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mpe_config
-- ----------------------------
DROP TABLE IF EXISTS `mpe_config`;
CREATE TABLE `mpe_config` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `examiner_num` int DEFAULT NULL,
  `layout_time` int DEFAULT NULL,
  `examination_num` int DEFAULT '0',
  `seatting_way` int DEFAULT NULL,
  `first_seating` int DEFAULT NULL,
  `is_seat_unify` int DEFAULT NULL,
  `is_seatway_unify` int DEFAULT '1',
  `tour_rule` varchar(48) DEFAULT NULL,
  `examinee_percent` int DEFAULT NULL,
  `city_opentime` timestamp NULL DEFAULT NULL,
  `county_opentime` timestamp NULL DEFAULT NULL,
  `exampoint_opentime` timestamp NULL DEFAULT NULL,
  `layout_number` int DEFAULT NULL,
  `layout_begin_time` int DEFAULT NULL,
  `parent_uid` varchar(128) DEFAULT NULL,
  `enable_children_orglevel` varchar(120) DEFAULT NULL,
  `examinee_data_status` tinyint(1) DEFAULT NULL,
  `status` varchar(4) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpe_examinfo
-- ----------------------------
DROP TABLE IF EXISTS `mpe_examinfo`;
CREATE TABLE `mpe_examinfo` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `year` varchar(16) NOT NULL,
  `et_id` varchar(24) DEFAULT NULL,
  `name` varchar(400) NOT NULL,
  `exam_abbreviation` varchar(320) DEFAULT NULL,
  `org_id` varchar(136) NOT NULL,
  `archive` int NOT NULL DEFAULT '0',
  `batch_no` varchar(8) DEFAULT NULL,
  `status` varchar(4) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpe_examsessioninfo
-- ----------------------------
DROP TABLE IF EXISTS `mpe_examsessioninfo`;
CREATE TABLE `mpe_examsessioninfo` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `name` varchar(200) NOT NULL,
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NOT NULL,
  `status` varchar(4) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpe_examsubjectinfo
-- ----------------------------
DROP TABLE IF EXISTS `mpe_examsubjectinfo`;
CREATE TABLE `mpe_examsubjectinfo` (
  `uid` varchar(200) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) DEFAULT NULL,
  `subject_code` varchar(40) NOT NULL,
  `name` varchar(200) NOT NULL,
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `sub_es_code` varchar(12) DEFAULT NULL,
  `sub_es_name` varchar(200) DEFAULT NULL,
  `sub_es_order` varchar(8) DEFAULT NULL,
  `status` varchar(4) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpp_building
-- ----------------------------
DROP TABLE IF EXISTS `mpp_building`;
CREATE TABLE `mpp_building` (
  `uid` varchar(200) NOT NULL,
  `building_name` varchar(400) NOT NULL,
  `building_floors` tinyint(1) DEFAULT NULL,
  `org_id` varchar(136) NOT NULL,
  `status` varchar(4) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpp_deviceinfo
-- ----------------------------
DROP TABLE IF EXISTS `mpp_deviceinfo`;
CREATE TABLE `mpp_deviceinfo` (
  `uid` varchar(160) NOT NULL,
  `org_id` varchar(136) NOT NULL,
  `sbdm` varchar(72) DEFAULT NULL,
  `dt_code` varchar(20) NOT NULL,
  `sbccbh` varchar(256) DEFAULT NULL,
  `name` varchar(400) DEFAULT NULL,
  `device_status` varchar(8) DEFAULT '1',
  `dev_mfr` varchar(200) DEFAULT NULL,
  `model` varchar(400) DEFAULT NULL,
  `sbpp` varchar(80) DEFAULT NULL,
  `parent_dev_id` varchar(800) DEFAULT NULL,
  `parent_dev_domain` varchar(2000) DEFAULT NULL,
  `osd_port` varchar(80) DEFAULT NULL,
  `channel_num` int DEFAULT NULL,
  `domain` varchar(2000) DEFAULT NULL,
  `dev_mac` varchar(128) DEFAULT NULL,
  `dev_ip` varchar(80) DEFAULT NULL,
  `dev_ipv6` varchar(800) DEFAULT NULL,
  `dev_port` int DEFAULT NULL,
  `login_name` varchar(200) DEFAULT NULL,
  `login_pwd` varchar(400) DEFAULT NULL,
  `hardware_ver` varchar(200) DEFAULT NULL,
  `software_ver` varchar(200) DEFAULT NULL,
  `firmware_ver` varchar(200) DEFAULT NULL,
  `purchase_time` timestamp NULL DEFAULT NULL,
  `warranty_time` timestamp NULL DEFAULT NULL,
  `scrap_year` varchar(12) DEFAULT NULL,
  `cam_resomax` varchar(120) DEFAULT NULL,
  `osd_max` int DEFAULT '0',
  `verify_ways` varchar(80) DEFAULT NULL,
  `block` tinyint(1) DEFAULT NULL,
  `wxdpdm` varchar(200) DEFAULT NULL,
  `rtsp` varchar(920) DEFAULT NULL,
  `function_type` varchar(8) DEFAULT NULL,
  `sim` varchar(512) DEFAULT NULL,
  `ar_code` varchar(24) DEFAULT NULL,
  `remark` varchar(2000) DEFAULT NULL,
  `status` varchar(4) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpp_exam_area
-- ----------------------------
DROP TABLE IF EXISTS `mpp_exam_area`;
CREATE TABLE `mpp_exam_area` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `examarea_no` varchar(80) NOT NULL,
  `examarea_name` varchar(320) DEFAULT NULL,
  `org_id` varchar(136) NOT NULL,
  `examarea_type` varchar(4) DEFAULT NULL,
  `examarea_level` varchar(4) DEFAULT NULL,
  `parent_uid` varchar(128) NOT NULL,
  `examorg_id_1` varchar(128) DEFAULT NULL,
  `examorg_id_2` varchar(128) DEFAULT NULL,
  `examorg_id_3` varchar(128) DEFAULT NULL,
  `examorg_id_4` varchar(128) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpp_exam_point
-- ----------------------------
DROP TABLE IF EXISTS `mpp_exam_point`;
CREATE TABLE `mpp_exam_point` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `exam_point_code` varchar(320) DEFAULT NULL,
  `exam_point_name` varchar(320) DEFAULT NULL,
  `org_id` varchar(136) NOT NULL,
  `parent_examorg_id` varchar(200) DEFAULT NULL,
  `point_setup_type` tinyint(1) DEFAULT '0',
  `examroom_num` int DEFAULT NULL,
  `examroom_backup_num` int DEFAULT NULL,
  `end_point` tinyint(1) DEFAULT '0',
  `hand_type` varchar(4) DEFAULT '2',
  `kdytdm` varchar(8) DEFAULT '11',
  `bz` varchar(1020) DEFAULT NULL,
  `zbfkpb` varchar(8) DEFAULT NULL,
  `zbfkpb_5g` varchar(8) DEFAULT NULL,
  `examorg_id_1` varchar(128) DEFAULT NULL,
  `examorg_id_2` varchar(128) DEFAULT NULL,
  `examorg_id_3` varchar(128) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpp_logicexamroom
-- ----------------------------
DROP TABLE IF EXISTS `mpp_logicexamroom`;
CREATE TABLE `mpp_logicexamroom` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `logic_exr_no` varchar(200) NOT NULL,
  `logic_name` varchar(160) DEFAULT NULL,
  `examinee_qty` int DEFAULT NULL,
  `bpkldm` varchar(40) DEFAULT NULL,
  `org_id` varchar(136) NOT NULL,
  `exampoint_uid` varchar(128) DEFAULT NULL,
  `kch` varchar(32) DEFAULT NULL,
  `status` varchar(4) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpp_orginfo
-- ----------------------------
DROP TABLE IF EXISTS `mpp_orginfo`;
CREATE TABLE `mpp_orginfo` (
  `uid` varchar(136) NOT NULL,
  `org_code` varchar(200) NOT NULL,
  `org_display_code` varchar(200) DEFAULT NULL,
  `org_ident_code` varchar(32) NOT NULL,
  `org_type` varchar(4) NOT NULL,
  `org_level` varchar(4) DEFAULT NULL,
  `jgbsm` varchar(200) DEFAULT NULL,
  `org_name` varchar(200) NOT NULL,
  `org_alias` varchar(80) DEFAULT NULL,
  `contact_name` varchar(80) DEFAULT NULL,
  `contact_tel` varchar(80) DEFAULT NULL,
  `technician_name` varchar(80) DEFAULT NULL,
  `technician_tel` varchar(80) DEFAULT NULL,
  `postal_code` varchar(24) DEFAULT NULL,
  `xxdm` varchar(32) DEFAULT NULL,
  `yxlxdm` varchar(8) DEFAULT NULL,
  `school_type` tinyint(1) DEFAULT NULL,
  `exam_area_code` varchar(320) DEFAULT NULL,
  `exam_area_name` varchar(320) DEFAULT NULL,
  `exam_point_type` varchar(4) DEFAULT NULL,
  `exam_point_code` varchar(320) DEFAULT NULL,
  `exam_point_name` varchar(320) DEFAULT NULL,
  `org_photo_url` varchar(800) DEFAULT NULL,
  `org_signature_url` varchar(800) DEFAULT NULL,
  `address` varchar(800) DEFAULT NULL,
  `latitude` varchar(80) DEFAULT NULL,
  `longitude` varchar(80) DEFAULT NULL,
  `fax` varchar(160) DEFAULT NULL,
  `web_address` varchar(400) DEFAULT NULL,
  `email` varchar(400) DEFAULT NULL,
  `parent_uid` varchar(136) NOT NULL,
  `org_id_1` varchar(136) DEFAULT NULL,
  `org_id_2` varchar(136) DEFAULT NULL,
  `org_id_3` varchar(136) DEFAULT NULL,
  `org_id_4` varchar(136) DEFAULT NULL,
  `status` varchar(4) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NOT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpp_placeinfo
-- ----------------------------
DROP TABLE IF EXISTS `mpp_placeinfo`;
CREATE TABLE `mpp_placeinfo` (
  `uid` varchar(200) NOT NULL,
  `org_id` varchar(136) NOT NULL,
  `csdm` varchar(80) NOT NULL DEFAULT '1',
  `csbsm` varchar(80) NOT NULL,
  `pt_code` varchar(8) NOT NULL,
  `building_id` varchar(128) DEFAULT NULL,
  `building_floors` tinyint(1) DEFAULT NULL,
  `name` varchar(400) DEFAULT NULL,
  `address` varchar(2000) DEFAULT NULL,
  `room_layout` tinyint(1) DEFAULT NULL,
  `room_cap` smallint DEFAULT NULL,
  `defaultseatting_way` smallint DEFAULT '1',
  `room_jc` varchar(200) DEFAULT NULL,
  `contact_tel` varchar(160) DEFAULT NULL,
  `sec_person` varchar(200) DEFAULT NULL,
  `contact_phone` varchar(44) DEFAULT NULL,
  `longitude` varchar(80) DEFAULT NULL,
  `latitude` varchar(80) DEFAULT NULL,
  `status` varchar(4) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpp_rel_device_place
-- ----------------------------
DROP TABLE IF EXISTS `mpp_rel_device_place`;
CREATE TABLE `mpp_rel_device_place` (
  `uid` varchar(148) NOT NULL,
  `pla_id` varchar(200) NOT NULL,
  `org_id` varchar(200) NOT NULL,
  `camera_pos` varchar(8) DEFAULT NULL,
  `dev_id` varchar(160) NOT NULL,
  `dev_pos` varchar(200) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpp_rel_logicexamroom_subject
-- ----------------------------
DROP TABLE IF EXISTS `mpp_rel_logicexamroom_subject`;
CREATE TABLE `mpp_rel_logicexamroom_subject` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) DEFAULT NULL,
  `logic_exr_no` varchar(200) DEFAULT NULL,
  `es_code` varchar(8) DEFAULT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `subject_code` varchar(40) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpp_rel_physicalexamroomenable
-- ----------------------------
DROP TABLE IF EXISTS `mpp_rel_physicalexamroomenable`;
CREATE TABLE `mpp_rel_physicalexamroomenable` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `org_id` varchar(136) NOT NULL,
  `pla_id` varchar(200) NOT NULL,
  `seatting_way` varchar(8) DEFAULT NULL,
  `first_seating` varchar(4) DEFAULT NULL,
  `logic_exr_no` varchar(80) DEFAULT NULL,
  `is_wc_pexroom` int DEFAULT NULL,
  `finish_dis` int DEFAULT NULL,
  `is_back_exroom` smallint DEFAULT '0',
  `bykclx` varchar(4) DEFAULT NULL,
  `back_name` varchar(120) DEFAULT NULL,
  `status` varchar(4) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpu_rel_user_role
-- ----------------------------
DROP TABLE IF EXISTS `mpu_rel_user_role`;
CREATE TABLE `mpu_rel_user_role` (
  `uid` varchar(128) NOT NULL,
  `user_id` varchar(128) DEFAULT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `ex_id` varchar(48) DEFAULT NULL,
  `org_id` varchar(144) DEFAULT NULL,
  `examorg_id` varchar(144) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpu_role
-- ----------------------------
DROP TABLE IF EXISTS `mpu_role`;
CREATE TABLE `mpu_role` (
  `code` varchar(12) NOT NULL,
  `role_name` varchar(40) DEFAULT NULL,
  `role_type` tinyint(1) DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpu_userinfo
-- ----------------------------
DROP TABLE IF EXISTS `mpu_userinfo`;
CREATE TABLE `mpu_userinfo` (
  `uid` varchar(128) NOT NULL,
  `account_name` varchar(128) NOT NULL,
  `order_num` int NOT NULL DEFAULT '0',
  `account_pwd` varchar(256) DEFAULT NULL,
  `account_type` char(1) NOT NULL,
  `is_default` char(1) NOT NULL DEFAULT '0',
  `staff_id` varchar(128) DEFAULT NULL,
  `examstaff_id` varchar(128) DEFAULT NULL,
  `ex_id` varchar(120) DEFAULT NULL,
  `post_code` varchar(120) DEFAULT NULL,
  `real_name` varchar(200) DEFAULT NULL,
  `phone` varchar(200) DEFAULT NULL,
  `cert_no` varchar(200) DEFAULT NULL,
  `gender` char(1) NOT NULL DEFAULT '0',
  `birth_date` varchar(40) DEFAULT NULL,
  `dep_id` varchar(128) DEFAULT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `expiration_date` timestamp NULL DEFAULT NULL,
  `account_status` char(1) NOT NULL DEFAULT '1',
  `status` char(1) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NOT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `init_instruct` varchar(80) DEFAULT '123456',
  `user_name` varchar(80) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpw_exam_staffinfo
-- ----------------------------
DROP TABLE IF EXISTS `mpw_exam_staffinfo`;
CREATE TABLE `mpw_exam_staffinfo` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(48) NOT NULL,
  `staff_id` varchar(128) DEFAULT NULL,
  `post_code` varchar(20) DEFAULT NULL,
  `layout_post` varchar(20) DEFAULT NULL,
  `draw_org` varchar(136) DEFAULT NULL,
  `send_org` varchar(136) DEFAULT NULL,
  `drawplan_id` varchar(128) DEFAULT NULL,
  `is_addto` char(1) NOT NULL DEFAULT '0',
  `is_addimport` char(1) NOT NULL DEFAULT '0',
  `is_adjust` char(1) NOT NULL DEFAULT '0',
  `lock_status` char(1) NOT NULL DEFAULT '0',
  `is_auto` char(1) NOT NULL DEFAULT '0',
  `org_id` varchar(136) DEFAULT NULL,
  `fax` varchar(80) DEFAULT NULL,
  `home_phone` varchar(80) DEFAULT NULL,
  `memo` varchar(200) DEFAULT NULL,
  `serial_number` varchar(12) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `fix_areacode` varchar(16) DEFAULT NULL,
  `fix_num` varchar(200) DEFAULT NULL,
  `duty_phone` varchar(400) DEFAULT NULL,
  `duty_code` varchar(80) DEFAULT NULL,
  `phone` varchar(200) DEFAULT NULL,
  `home_areacode` varchar(16) DEFAULT NULL,
  `make_card_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `real_name` varchar(200) NOT NULL DEFAULT '',
  `cert_no` varchar(200) DEFAULT NULL,
  `gender` char(1) NOT NULL DEFAULT '0',
  `birth_date` varchar(40) DEFAULT NULL,
  `is_business` char(1) NOT NULL DEFAULT '0',
  `photo_url` varchar(400) DEFAULT NULL,
  `sub_logo` char(1) DEFAULT NULL,
  `prepared_escodes` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpw_layout_examiner
-- ----------------------------
DROP TABLE IF EXISTS `mpw_layout_examiner`;
CREATE TABLE `mpw_layout_examiner` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(48) NOT NULL,
  `exam_staff_id` varchar(128) DEFAULT NULL,
  `es_code` varchar(8) DEFAULT NULL,
  `post_code` varchar(20) DEFAULT NULL,
  `group_no` varchar(12) DEFAULT NULL,
  `examiner_type` char(1) NOT NULL DEFAULT '1',
  `logic_exr_no` varchar(200) DEFAULT NULL,
  `loginc_group_no` varchar(40) DEFAULT NULL,
  `layout_type` char(1) NOT NULL DEFAULT '1',
  `layout_rule` varchar(200) DEFAULT NULL,
  `is_layout_rule` char(1) NOT NULL,
  `un_rule_memo` varchar(200) DEFAULT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `rand_num` int NOT NULL DEFAULT '0',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `logic_rand_num` int DEFAULT NULL,
  `pla_id` varchar(200) DEFAULT NULL,
  `physical_group_no` varchar(40) DEFAULT '00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpw_layout_inspector
-- ----------------------------
DROP TABLE IF EXISTS `mpw_layout_inspector`;
CREATE TABLE `mpw_layout_inspector` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(48) NOT NULL,
  `exam_staff_id` varchar(128) DEFAULT NULL,
  `user_id` varchar(128) DEFAULT NULL,
  `account_name` varchar(128) NOT NULL,
  `post_code` varchar(20) DEFAULT NULL,
  `pt_id` varchar(8) DEFAULT NULL,
  `inspect_org` varchar(136) DEFAULT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for mpw_layout_invigilator
-- ----------------------------
DROP TABLE IF EXISTS `mpw_layout_invigilator`;
CREATE TABLE `mpw_layout_invigilator` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(48) NOT NULL,
  `exam_staff_id` varchar(128) DEFAULT NULL,
  `es_code` varchar(8) DEFAULT NULL,
  `post_code` varchar(20) DEFAULT NULL,
  `physical_exr_id` varchar(400) NOT NULL,
  `layout_type` char(1) NOT NULL DEFAULT '1',
  `layout_rule` char(1) NOT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for p_abnormal_behavior
-- ----------------------------
DROP TABLE IF EXISTS `p_abnormal_behavior`;
CREATE TABLE `p_abnormal_behavior` (
  `uid` varchar(48) NOT NULL,
  `type` tinyint(1) NOT NULL,
  `type_name` varchar(120) DEFAULT NULL,
  `code` tinyint(1) NOT NULL,
  `name` varchar(120) DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for p_behavior
-- ----------------------------
DROP TABLE IF EXISTS `p_behavior`;
CREATE TABLE `p_behavior` (
  `uid` varchar(80) NOT NULL,
  `et_id` varchar(12) NOT NULL,
  `behavior_no` tinyint(1) DEFAULT NULL,
  `behavior_name` varchar(400) DEFAULT NULL,
  `detail_no` tinyint(1) DEFAULT NULL,
  `detail_name` varchar(400) DEFAULT NULL,
  `check_time_type` tinyint(1) DEFAULT NULL,
  `check_time_offset` tinyint(1) DEFAULT NULL,
  `remark` varchar(800) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for p_code
-- ----------------------------
DROP TABLE IF EXISTS `p_code`;
CREATE TABLE `p_code` (
  `uid` varchar(48) NOT NULL,
  `type` varchar(24) NOT NULL,
  `code` varchar(24) NOT NULL,
  `name` varchar(120) DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for p_dutycheck
-- ----------------------------
DROP TABLE IF EXISTS `p_dutycheck`;
CREATE TABLE `p_dutycheck` (
  `uid` varchar(80) NOT NULL,
  `et_id` varchar(48) DEFAULT NULL,
  `dutycheck_no` tinyint(1) DEFAULT NULL,
  `dutycheck_name` varchar(400) DEFAULT NULL,
  `detail_no` tinyint(1) DEFAULT NULL,
  `detail_name` varchar(400) DEFAULT NULL,
  `check_time_type` tinyint(1) DEFAULT NULL,
  `check_time_offset` tinyint(1) DEFAULT NULL,
  `remark` varchar(800) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for p_post
-- ----------------------------
DROP TABLE IF EXISTS `p_post`;
CREATE TABLE `p_post` (
  `code` varchar(20) NOT NULL,
  `name` varchar(40) NOT NULL,
  `type` varchar(8) NOT NULL,
  `type_name` varchar(80) NOT NULL,
  `is_exschool` char(1) DEFAULT NULL,
  `symbol` char(1) DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for s_dutycheck
-- ----------------------------
DROP TABLE IF EXISTS `s_dutycheck`;
CREATE TABLE `s_dutycheck` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) DEFAULT NULL,
  `es_code` varchar(8) DEFAULT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `org_name` varchar(400) DEFAULT NULL,
  `org_status` varchar(4) DEFAULT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `dutycheck_detail_id` varchar(128) DEFAULT NULL,
  `resp_num` int DEFAULT NULL,
  `resp_en_num` int DEFAULT NULL,
  `lt1_en_num` int DEFAULT NULL,
  `lt3_en_num` int DEFAULT NULL,
  `gt3_en_num` int DEFAULT NULL,
  `sum_time` int DEFAULT NULL,
  `resp_bj_num` int DEFAULT NULL,
  `resp_bj_en_num` int DEFAULT NULL,
  `lt1_bj_en_num` int DEFAULT NULL,
  `lt3_bj_en_num` int DEFAULT NULL,
  `gt3_bj_en_num` int DEFAULT NULL,
  `sum_bj_time` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for s_es_org_datacount
-- ----------------------------
DROP TABLE IF EXISTS `s_es_org_datacount`;
CREATE TABLE `s_es_org_datacount` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `org_id` varchar(34) NOT NULL,
  `org_name` varchar(400) DEFAULT NULL,
  `lexroom_en_num` int DEFAULT '0',
  `behavior_comp_normal_num` int DEFAULT '0',
  `behavior_comp_fault_num` int DEFAULT '0',
  `behavior_incomp_normal_num` int DEFAULT '0',
  `behavior_incomp_fault_num` int DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for s_randomcheck
-- ----------------------------
DROP TABLE IF EXISTS `s_randomcheck`;
CREATE TABLE `s_randomcheck` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) DEFAULT NULL,
  `es_code` varchar(8) DEFAULT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `org_name` varchar(400) DEFAULT NULL,
  `org_status` varchar(4) DEFAULT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `random_detail_id` varchar(128) DEFAULT NULL,
  `resp_num` int DEFAULT NULL,
  `resp_en_num` int DEFAULT NULL,
  `lt1_en_num` int DEFAULT NULL,
  `lt3_en_num` int DEFAULT NULL,
  `gt3_en_num` int DEFAULT NULL,
  `sum_time` int DEFAULT NULL,
  `resp_bj_num` int DEFAULT NULL,
  `resp_bj_en_num` int DEFAULT NULL,
  `lt1_bj_en_num` int DEFAULT NULL,
  `lt3_bj_en_num` int DEFAULT NULL,
  `gt3_bj_en_num` int DEFAULT NULL,
  `sum_bj_time` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_behavior
-- ----------------------------
DROP TABLE IF EXISTS `vis_behavior`;
CREATE TABLE `vis_behavior` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `behavior_name` varchar(400) DEFAULT NULL,
  `remark` varchar(800) DEFAULT NULL,
  `status` char(1) NOT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_behavior_check
-- ----------------------------
DROP TABLE IF EXISTS `vis_behavior_check`;
CREATE TABLE `vis_behavior_check` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(30) NOT NULL,
  `es_code` varchar(2) NOT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `logic_exr_no` varchar(50) NOT NULL,
  `pla_id` varchar(200) DEFAULT NULL,
  `behavior_id` varchar(128) DEFAULT NULL,
  `behavior_detail_id` varchar(128) DEFAULT NULL,
  `check_time` timestamp NULL DEFAULT NULL,
  `check_result` tinyint(1) DEFAULT NULL,
  `remark` varchar(800) DEFAULT NULL,
  `user_id` varchar(200) NOT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `report_time` timestamp NULL DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_behavior_detail
-- ----------------------------
DROP TABLE IF EXISTS `vis_behavior_detail`;
CREATE TABLE `vis_behavior_detail` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `behavior_id` varchar(128) NOT NULL,
  `name` varchar(400) DEFAULT NULL,
  `check_time_type` tinyint(1) DEFAULT NULL,
  `check_time_offset` tinyint(1) DEFAULT NULL,
  `remark` varchar(800) DEFAULT NULL,
  `status` char(1) NOT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_code
-- ----------------------------
DROP TABLE IF EXISTS `vis_code`;
CREATE TABLE `vis_code` (
  `uid` varchar(60) NOT NULL,
  `type` varchar(32) NOT NULL,
  `code` varchar(24) NOT NULL,
  `name` varchar(120) DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_config
-- ----------------------------
DROP TABLE IF EXISTS `vis_config`;
CREATE TABLE `vis_config` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `behavior_status` tinyint(1) DEFAULT '0',
  `behavior_roles` varchar(80) DEFAULT NULL,
  `dutycheck_status` tinyint(1) DEFAULT '0',
  `dutycheck_roles` varchar(80) DEFAULT NULL,
  `randomcheck_interval` tinyint(1) DEFAULT '5',
  `inspect_poll_switch` tinyint(1) DEFAULT NULL,
  `examinee_open_time` timestamp NULL DEFAULT NULL,
  `examiner_open_time` timestamp NULL DEFAULT NULL,
  `dutycheck_resp_method` tinyint(1) DEFAULT '2',
  `dutycheck_resp_duration` tinyint(1) DEFAULT '5',
  `behavior_resp_mehtod` tinyint(1) DEFAULT '2',
  `behavior_resp_duration` tinyint(1) DEFAULT '20',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `behavior_publish_time` timestamp NULL DEFAULT NULL,
  `dutycheck_publish_time` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_dutycheck
-- ----------------------------
DROP TABLE IF EXISTS `vis_dutycheck`;
CREATE TABLE `vis_dutycheck` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `detycheck_name` varchar(400) DEFAULT NULL,
  `remark` varchar(800) DEFAULT NULL,
  `status` char(1) NOT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_dutycheck_detail
-- ----------------------------
DROP TABLE IF EXISTS `vis_dutycheck_detail`;
CREATE TABLE `vis_dutycheck_detail` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `dutycheck_id` varchar(128) NOT NULL,
  `dutycheck_name` varchar(400) DEFAULT NULL,
  `check_time_type` tinyint(1) DEFAULT NULL,
  `check_time_offset` smallint DEFAULT NULL,
  `remark` varchar(800) DEFAULT NULL,
  `status` char(1) NOT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_dutycheck_response
-- ----------------------------
DROP TABLE IF EXISTS `vis_dutycheck_response`;
CREATE TABLE `vis_dutycheck_response` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(30) NOT NULL,
  `es_code` varchar(2) NOT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `dutycheck_id` varchar(128) DEFAULT NULL,
  `dutycheck_detail_id` varchar(128) DEFAULT NULL,
  `response_result` tinyint(1) DEFAULT NULL,
  `user_id` varchar(200) NOT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `report_time` timestamp NULL DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_examinee_check_in
-- ----------------------------
DROP TABLE IF EXISTS `vis_examinee_check_in`;
CREATE TABLE `vis_examinee_check_in` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `org_id` varchar(136) NOT NULL,
  `logic_exr_no` varchar(200) NOT NULL,
  `pla_id` varchar(200) DEFAULT NULL,
  `check_in_result` tinyint(1) NOT NULL DEFAULT '1',
  `name` varchar(200) DEFAULT NULL,
  `gender_id` varchar(20) DEFAULT NULL,
  `zkzh` varchar(200) NOT NULL,
  `seat_no` tinyint(1) DEFAULT NULL,
  `kskmdm` varchar(40) DEFAULT NULL,
  `kskmmc` varchar(200) DEFAULT NULL,
  `cert_no` varchar(200) DEFAULT NULL,
  `photo_url` varchar(400) DEFAULT NULL,
  `onsite_photo_url` varchar(400) DEFAULT NULL,
  `user_id` varchar(128) DEFAULT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `report_time` timestamp NULL DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL,
  `status` char(1) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_examroom_abbehavior
-- ----------------------------
DROP TABLE IF EXISTS `vis_examroom_abbehavior`;
CREATE TABLE `vis_examroom_abbehavior` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `org_id` varchar(136) NOT NULL,
  `logic_exr_no` varchar(200) NOT NULL,
  `pla_id` varchar(200) DEFAULT NULL,
  `abbeh_type` tinyint(1) NOT NULL,
  `abbeh_code` tinyint(1) NOT NULL,
  `abbeh_time` timestamp NULL DEFAULT NULL,
  `report_way` tinyint(1) DEFAULT '2',
  `user_id` varchar(128) DEFAULT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `report_time` timestamp NULL DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL,
  `status` char(1) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_examroom_abbehavior_file
-- ----------------------------
DROP TABLE IF EXISTS `vis_examroom_abbehavior_file`;
CREATE TABLE `vis_examroom_abbehavior_file` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `abbeh_id` varchar(128) NOT NULL,
  `file_type` tinyint(1) DEFAULT NULL,
  `file_url` varchar(800) DEFAULT NULL,
  `file_name` varchar(400) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_examroom_absence
-- ----------------------------
DROP TABLE IF EXISTS `vis_examroom_absence`;
CREATE TABLE `vis_examroom_absence` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `org_id` varchar(136) NOT NULL,
  `logic_exr_no` varchar(200) NOT NULL,
  `pla_id` varchar(200) DEFAULT NULL,
  `absence_num` decimal(22,0) DEFAULT NULL,
  `user_id` varchar(128) DEFAULT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `report_time` timestamp NULL DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL,
  `status` char(1) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_examroom_behavior_status
-- ----------------------------
DROP TABLE IF EXISTS `vis_examroom_behavior_status`;
CREATE TABLE `vis_examroom_behavior_status` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(48) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `logic_exr_no` varchar(50) NOT NULL,
  `pla_id` varchar(200) DEFAULT NULL,
  `behavior_id` varchar(128) NOT NULL,
  `behavior_num` smallint DEFAULT NULL,
  `behavior_detail_normal` smallint DEFAULT NULL,
  `behavior_detail_fault` smallint DEFAULT NULL,
  `user_id` varchar(200) NOT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `last_report_time` timestamp NULL DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_examroom_camera_check
-- ----------------------------
DROP TABLE IF EXISTS `vis_examroom_camera_check`;
CREATE TABLE `vis_examroom_camera_check` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(48) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `org_id` varchar(136) NOT NULL,
  `logic_exr_no` varchar(200) NOT NULL,
  `pla_id` varchar(400) DEFAULT NULL,
  `device_id` varchar(40) DEFAULT NULL,
  `check_result` varchar(40) NOT NULL DEFAULT '1',
  `remark` varchar(400) DEFAULT NULL,
  `user_id` varchar(200) DEFAULT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_examroom_camera_file
-- ----------------------------
DROP TABLE IF EXISTS `vis_examroom_camera_file`;
CREATE TABLE `vis_examroom_camera_file` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `org_id` varchar(136) NOT NULL,
  `logic_exr_no` varchar(200) NOT NULL,
  `pla_id` varchar(400) DEFAULT NULL,
  `device_id` varchar(40) DEFAULT NULL,
  `file_type` tinyint(1) DEFAULT NULL,
  `file_url` varchar(800) DEFAULT NULL,
  `file_name` varchar(400) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_examroom_camera_status
-- ----------------------------
DROP TABLE IF EXISTS `vis_examroom_camera_status`;
CREATE TABLE `vis_examroom_camera_status` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `org_id` varchar(136) NOT NULL,
  `logic_exr_no` varchar(200) NOT NULL,
  `pla_id` varchar(200) DEFAULT NULL,
  `camera_status` tinyint(1) NOT NULL DEFAULT '0',
  `user_id` varchar(128) DEFAULT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `report_time` timestamp NULL DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_instruct
-- ----------------------------
DROP TABLE IF EXISTS `vis_instruct`;
CREATE TABLE `vis_instruct` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(48) NOT NULL,
  `instruct_title` varchar(200) NOT NULL,
  `instruct_content` varchar(800) NOT NULL,
  `send_user` varchar(128) DEFAULT NULL,
  `send_org_id` varchar(200) DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL,
  `status` char(1) NOT NULL DEFAULT 'A',
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_randomcheck_detail
-- ----------------------------
DROP TABLE IF EXISTS `vis_randomcheck_detail`;
CREATE TABLE `vis_randomcheck_detail` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `es_code` varchar(8) DEFAULT NULL,
  `randomcheck_name` varchar(400) DEFAULT NULL,
  `randomcheck_time` timestamp NULL DEFAULT NULL,
  `randomcheck_roles` varchar(80) DEFAULT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `remark` varchar(800) DEFAULT NULL,
  `status` char(1) NOT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_randomcheck_response
-- ----------------------------
DROP TABLE IF EXISTS `vis_randomcheck_response`;
CREATE TABLE `vis_randomcheck_response` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(30) NOT NULL,
  `es_code` varchar(2) NOT NULL,
  `org_id` varchar(136) DEFAULT NULL,
  `randomcheck_detail_id` varchar(128) DEFAULT NULL,
  `response_result` tinyint(1) DEFAULT NULL,
  `user_id` varchar(200) NOT NULL,
  `role_id` varchar(12) DEFAULT NULL,
  `report_time` timestamp NULL DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_rel_abbehavior_examinee
-- ----------------------------
DROP TABLE IF EXISTS `vis_rel_abbehavior_examinee`;
CREATE TABLE `vis_rel_abbehavior_examinee` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `abbeh_id` varchar(128) NOT NULL,
  `name` varchar(200) DEFAULT NULL,
  `gender_id` varchar(20) DEFAULT NULL,
  `zkzh` varchar(200) NOT NULL,
  `seat_no` tinyint(1) DEFAULT NULL,
  `kskmdm` varchar(40) DEFAULT NULL,
  `kskmmc` varchar(200) DEFAULT NULL,
  `cert_no` varchar(200) DEFAULT NULL,
  `photo_url` varchar(400) DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_rel_abbehavior_examineer
-- ----------------------------
DROP TABLE IF EXISTS `vis_rel_abbehavior_examineer`;
CREATE TABLE `vis_rel_abbehavior_examineer` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `abbeh_id` varchar(128) NOT NULL,
  `exam_staff_id` varchar(128) NOT NULL,
  `remark` varchar(400) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_rel_absence_examinee
-- ----------------------------
DROP TABLE IF EXISTS `vis_rel_absence_examinee`;
CREATE TABLE `vis_rel_absence_examinee` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `absence_id` varchar(128) NOT NULL,
  `name` varchar(200) DEFAULT NULL,
  `gender_id` varchar(20) DEFAULT NULL,
  `zkzh` varchar(200) NOT NULL,
  `seat_no` tinyint(1) DEFAULT NULL,
  `kskmdm` varchar(40) DEFAULT NULL,
  `kskmmc` varchar(200) DEFAULT NULL,
  `cert_no` varchar(200) DEFAULT NULL,
  `photo_url` varchar(400) DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_rel_behavior_subject
-- ----------------------------
DROP TABLE IF EXISTS `vis_rel_behavior_subject`;
CREATE TABLE `vis_rel_behavior_subject` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `behavior_id` varchar(128) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `subject_code` varchar(40) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_rel_dutycheck_escode
-- ----------------------------
DROP TABLE IF EXISTS `vis_rel_dutycheck_escode`;
CREATE TABLE `vis_rel_dutycheck_escode` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `dutycheck_id` varchar(128) NOT NULL,
  `es_code` varchar(8) NOT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for vis_rel_instruct_user
-- ----------------------------
DROP TABLE IF EXISTS `vis_rel_instruct_user`;
CREATE TABLE `vis_rel_instruct_user` (
  `uid` varchar(128) NOT NULL,
  `ex_id` varchar(120) NOT NULL,
  `instruct_id` varchar(128) NOT NULL,
  `receive_user` varchar(128) NOT NULL,
  `receive_role` varchar(12) DEFAULT NULL,
  `receive_org_id` varchar(136) DEFAULT NULL,
  `read_status` tinyint(1) DEFAULT '0',
  `read_time` timestamp NULL DEFAULT NULL,
  `remark` varchar(400) DEFAULT NULL,
  `create_by` varchar(128) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(128) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

SET FOREIGN_KEY_CHECKS = 1;
