-- MySQL建表脚本 - 基于达梦数据库表结构
-- 创建日期: 2024-07-30
-- 说明: 用于创建与达梦数据库对应的MySQL表结构

-- 创建数据库
CREATE DATABASE IF NOT EXISTS sync_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sync_db;

-- 1. mpe_config 表
DROP TABLE IF EXISTS mpe_config;
CREATE TABLE mpe_config (
    uid VARCHAR(128) NOT NULL COMMENT '唯一标识',
    ex_id VARCHAR(120) NOT NULL COMMENT '考试ID',
    examiner_num INT DEFAULT NULL COMMENT '监考员数量',
    layout_time INT DEFAULT NULL COMMENT '布局时间',
    examination_num INT DEFAULT 0 COMMENT '考试数量',
    seatting_way INT DEFAULT NULL COMMENT '座位方式',
    first_seating INT DEFAULT NULL COMMENT '首次座位',
    is_seat_unify INT DEFAULT NULL COMMENT '座位是否统一',
    is_seatway_unify INT DEFAULT 1 COMMENT '座位方式是否统一',
    tour_rule VARCHAR(48) DEFAULT NULL COMMENT '巡考规则',
    examinee_percent INT DEFAULT NULL COMMENT '考生百分比',
    city_opentime TIMESTAMP NULL DEFAULT NULL COMMENT '市级开放时间',
    county_opentime TIMESTAMP NULL DEFAULT NULL COMMENT '县级开放时间',
    exampoint_opentime TIMESTAMP NULL DEFAULT NULL COMMENT '考点开放时间',
    layout_number INT DEFAULT NULL COMMENT '布局编号',
    layout_begin_time INT DEFAULT NULL COMMENT '布局开始时间',
    parent_uid VARCHAR(128) DEFAULT NULL COMMENT '父级UID',
    enable_children_orglevel VARCHAR(120) DEFAULT NULL COMMENT '启用子级组织层级',
    examinee_data_status TINYINT DEFAULT NULL COMMENT '考生数据状态',
    status VARCHAR(4) NOT NULL DEFAULT 'A' COMMENT '状态',
    create_by VARCHAR(128) DEFAULT NULL COMMENT '创建人',
    create_time TIMESTAMP NULL DEFAULT NULL COMMENT '创建时间',
    update_by VARCHAR(128) DEFAULT NULL COMMENT '更新人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (uid, ex_id),
    INDEX idx_ex_id (ex_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试配置表';

-- 2. mpe_examinfo 表
DROP TABLE IF EXISTS mpe_examinfo;
CREATE TABLE mpe_examinfo (
    uid VARCHAR(128) NOT NULL COMMENT '唯一标识',
    ex_id VARCHAR(120) NOT NULL COMMENT '考试ID',
    year VARCHAR(16) NOT NULL COMMENT '年份',
    et_id VARCHAR(24) DEFAULT NULL COMMENT '考试类型ID',
    name VARCHAR(400) NOT NULL COMMENT '考试名称',
    exam_abbreviation VARCHAR(320) DEFAULT NULL COMMENT '考试简称',
    org_id VARCHAR(136) NOT NULL COMMENT '组织ID',
    archive INT NOT NULL DEFAULT 0 COMMENT '归档状态',
    batch_no VARCHAR(8) DEFAULT NULL COMMENT '批次号',
    status VARCHAR(4) NOT NULL DEFAULT 'A' COMMENT '状态',
    create_by VARCHAR(128) DEFAULT NULL COMMENT '创建人',
    create_time TIMESTAMP NULL DEFAULT NULL COMMENT '创建时间',
    update_by VARCHAR(128) DEFAULT NULL COMMENT '更新人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (uid, ex_id, year),
    INDEX idx_ex_id (ex_id),
    INDEX idx_year (year),
    INDEX idx_org_id (org_id),
    INDEX idx_status (status),
    INDEX idx_name (name(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试信息表';

-- 3. mpe_examsessioninfo 表
DROP TABLE IF EXISTS mpe_examsessioninfo;
CREATE TABLE mpe_examsessioninfo (
    uid VARCHAR(128) NOT NULL COMMENT '唯一标识',
    ex_id VARCHAR(120) NOT NULL COMMENT '考试ID',
    es_code VARCHAR(8) NOT NULL COMMENT '考试场次代码',
    name VARCHAR(200) NOT NULL COMMENT '场次名称',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '结束时间',
    status VARCHAR(4) NOT NULL DEFAULT 'A' COMMENT '状态',
    create_by VARCHAR(128) DEFAULT NULL COMMENT '创建人',
    create_time TIMESTAMP NULL DEFAULT NULL COMMENT '创建时间',
    update_by VARCHAR(128) DEFAULT NULL COMMENT '更新人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (uid, ex_id, es_code),
    INDEX idx_ex_id (ex_id),
    INDEX idx_es_code (es_code),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试场次信息表';

-- 数据类型映射说明
/*
达梦数据库 -> MySQL 映射关系:
1. VARCHAR -> VARCHAR (保持长度一致)
2. INT -> INT (4字节整数)
3. TINYINT -> TINYINT (1字节整数)
4. TIMESTAMP -> TIMESTAMP (时间戳类型)

注意事项:
1. MySQL中的TIMESTAMP字段默认值处理与达梦略有不同
2. 字符集统一使用utf8mb4以支持完整的Unicode字符
3. 主键和索引根据业务需求进行了优化
4. 添加了适当的注释以便维护
*/

-- 创建同步状态监控表
DROP TABLE IF EXISTS sync_monitor;
CREATE TABLE sync_monitor (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(64) NOT NULL COMMENT '表名',
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '同步时间',
    record_count INT DEFAULT 0 COMMENT '同步记录数',
    status VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '同步状态',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    INDEX idx_table_name (table_name),
    INDEX idx_sync_time (sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='同步监控表';

-- 插入初始监控记录
INSERT INTO sync_monitor (table_name, record_count, status) VALUES
('mpe_config', 0, 'READY'),
('mpe_examinfo', 0, 'READY'),
('mpe_examsessioninfo', 0, 'READY');

-- 创建用于数据验证的视图
CREATE OR REPLACE VIEW v_sync_summary AS
SELECT 
    table_name,
    MAX(sync_time) as last_sync_time,
    SUM(record_count) as total_records,
    COUNT(*) as sync_count
FROM sync_monitor 
WHERE status = 'SUCCESS'
GROUP BY table_name;

-- 显示创建结果
SELECT 'Tables created successfully!' as result;
SHOW TABLES;
