-- 由 USER_IND_COLUMNS_202507300903.txt 自动生成的 MySQL 索引语句
-- 表名和字段名全部小写

-- mpe_config 表索引
CREATE INDEX idx_mpe_config_uid ON mpe_config(uid);

-- mpe_examinfo 表索引
CREATE INDEX idx_mpe_examinfo_ex_id ON mpe_examinfo(ex_id);
CREATE INDEX idx_mpe_examinfo_uid ON mpe_examinfo(uid);

-- mpe_examsessioninfo 表索引
CREATE INDEX idx_mpe_examsessioninfo_ex_id ON mpe_examsessioninfo(ex_id);
CREATE INDEX idx_mpe_examsessioninfo_es_code ON mpe_examsessioninfo(es_code);
CREATE INDEX idx_mpe_examsessioninfo_uid ON mpe_examsessioninfo(uid);

-- mpe_examsubjectinfo 表索引
CREATE INDEX idx_mpe_examsubjectinfo_ex_id ON mpe_examsubjectinfo(ex_id);
CREATE INDEX idx_mpe_examsubjectinfo_es_code ON mpe_examsubjectinfo(es_code);
CREATE INDEX idx_mpe_examsubjectinfo_subject_code ON mpe_examsubjectinfo(subject_code);
CREATE INDEX idx_mpe_examsubjectinfo_uid ON mpe_examsubjectinfo(uid);

-- mpp_building 表索引
CREATE INDEX idx_mpp_building_org_id ON mpp_building(org_id);
CREATE INDEX idx_mpp_building_uid ON mpp_building(uid);

-- mpp_deviceinfo 表索引
CREATE INDEX idx_mpp_deviceinfo_org_id ON mpp_deviceinfo(org_id);
CREATE INDEX idx_mpp_deviceinfo_dt_code ON mpp_deviceinfo(dt_code);
CREATE INDEX idx_mpp_deviceinfo_status ON mpp_deviceinfo(status);
CREATE INDEX idx_mpp_deviceinfo_uid ON mpp_deviceinfo(uid);

-- mpp_exam_area 表索引
CREATE INDEX idx_mpp_exam_area_ex_id ON mpp_exam_area(ex_id);
CREATE INDEX idx_mpp_exam_area_org_id ON mpp_exam_area(org_id);
CREATE INDEX idx_mpp_exam_area_uid ON mpp_exam_area(uid);

-- mpp_exam_point 表索引
CREATE INDEX idx_mpp_exam_point_ex_id ON mpp_exam_point(ex_id);
CREATE INDEX idx_mpp_exam_point_parent_examorg_id ON mpp_exam_point(parent_examorg_id);
CREATE INDEX idx_mpp_exam_point_exam_point_code ON mpp_exam_point(exam_point_code);
CREATE INDEX idx_mpp_exam_point_uid ON mpp_exam_point(uid);

-- mpp_logicexamroom 表索引
CREATE INDEX idx_mpp_logicexamroom_ex_id ON mpp_logicexamroom(ex_id);
CREATE INDEX idx_mpp_logicexamroom_es_code ON mpp_logicexamroom(es_code);
CREATE INDEX idx_mpp_logicexamroom_org_id ON mpp_logicexamroom(org_id);
CREATE INDEX idx_mpp_logicexamroom_uid ON mpp_logicexamroom(uid);

-- mpp_orginfo 表索引
CREATE INDEX idx_mpp_orginfo_org_id_1 ON mpp_orginfo(org_id_1);
CREATE INDEX idx_mpp_orginfo_org_id_2 ON mpp_orginfo(org_id_2);
CREATE INDEX idx_mpp_orginfo_org_id_3 ON mpp_orginfo(org_id_3);
CREATE INDEX idx_mpp_orginfo_org_id_4 ON mpp_orginfo(org_id_4);
CREATE INDEX idx_mpp_orginfo_parent_uid ON mpp_orginfo(parent_uid);
CREATE INDEX idx_mpp_orginfo_uid ON mpp_orginfo(uid);

-- mpp_placeinfo 表索引
CREATE INDEX idx_mpp_placeinfo_building_id ON mpp_placeinfo(building_id);
CREATE INDEX idx_mpp_placeinfo_org_id ON mpp_placeinfo(org_id);
CREATE INDEX idx_mpp_placeinfo_pt_code ON mpp_placeinfo(pt_code);
CREATE INDEX idx_mpp_placeinfo_status ON mpp_placeinfo(status);
CREATE INDEX idx_mpp_placeinfo_uid ON mpp_placeinfo(uid);

-- mpp_rel_device_place 表索引
CREATE INDEX idx_mpp_rel_device_place_uid ON mpp_rel_device_place(uid);

-- mpp_rel_logicexamroom_subject 表索引
CREATE INDEX idx_mpp_rel_logicexamroom_subject_ex_id ON mpp_rel_logicexamroom_subject(ex_id);
CREATE INDEX idx_mpp_rel_logicexamroom_subject_es_code ON mpp_rel_logicexamroom_subject(es_code);
CREATE INDEX idx_mpp_rel_logicexamroom_subject_logic_exr_no ON mpp_rel_logicexamroom_subject(logic_exr_no);
CREATE INDEX idx_mpp_rel_logicexamroom_subject_uid ON mpp_rel_logicexamroom_subject(uid);

-- mpp_rel_physicalexamroomenable 表索引
CREATE INDEX idx_mpp_rel_physicalexamroomenable_ex_id ON mpp_rel_physicalexamroomenable(ex_id);
CREATE INDEX idx_mpp_rel_physicalexamroomenable_es_code ON mpp_rel_physicalexamroomenable(es_code);
CREATE INDEX idx_mpp_rel_physicalexamroomenable_logic_exr_no ON mpp_rel_physicalexamroomenable(logic_exr_no);
CREATE INDEX idx_mpp_rel_physicalexamroomenable_org_id ON mpp_rel_physicalexamroomenable(org_id);
CREATE INDEX idx_mpp_rel_physicalexamroomenable_pla_id ON mpp_rel_physicalexamroomenable(pla_id);
CREATE INDEX idx_mpp_rel_physicalexamroomenable_uid ON mpp_rel_physicalexamroomenable(uid);

-- mpu_rel_user_role 表索引
CREATE INDEX idx_mpu_rel_user_role_examorg_id ON mpu_rel_user_role(examorg_id);
CREATE INDEX idx_mpu_rel_user_role_uid ON mpu_rel_user_role(uid);
CREATE INDEX idx_mpu_rel_user_role_org_id ON mpu_rel_user_role(org_id);
CREATE INDEX idx_mpu_rel_user_role_role_id ON mpu_rel_user_role(role_id);
CREATE INDEX idx_mpu_rel_user_role_user_id ON mpu_rel_user_role(user_id);
CREATE INDEX idx_mpu_rel_user_role_update_time ON mpu_rel_user_role(update_time);

-- mpu_role 表索引
CREATE INDEX idx_mpu_role_code ON mpu_role(code);

-- mpu_userinfo 表索引
CREATE INDEX idx_mpu_userinfo_account_type ON mpu_userinfo(account_type);
CREATE INDEX idx_mpu_userinfo_uid ON mpu_userinfo(uid);
CREATE INDEX idx_mpu_userinfo_staff_id ON mpu_userinfo(staff_id);
CREATE INDEX idx_mpu_userinfo_status ON mpu_userinfo(status);

-- mpw_exam_staffinfo 表索引
CREATE INDEX idx_mpw_exam_staffinfo_ex_id ON mpw_exam_staffinfo(ex_id);
CREATE INDEX idx_mpw_exam_staffinfo_draw_org ON mpw_exam_staffinfo(draw_org);
CREATE INDEX idx_mpw_exam_staffinfo_drawplan_id ON mpw_exam_staffinfo(drawplan_id);
CREATE INDEX idx_mpw_exam_staffinfo_staff_id ON mpw_exam_staffinfo(staff_id);
CREATE INDEX idx_mpw_exam_staffinfo_org_id ON mpw_exam_staffinfo(org_id);
CREATE INDEX idx_mpw_exam_staffinfo_uid ON mpw_exam_staffinfo(uid);
CREATE INDEX idx_mpw_exam_staffinfo_layout_post ON mpw_exam_staffinfo(layout_post);
CREATE INDEX idx_mpw_exam_staffinfo_send_org ON mpw_exam_staffinfo(send_org);

-- mpw_layout_examiner 表索引
CREATE INDEX idx_mpw_layout_examiner_ex_id ON mpw_layout_examiner(ex_id);
CREATE INDEX idx_mpw_layout_examiner_org_id ON mpw_layout_examiner(org_id);
CREATE INDEX idx_mpw_layout_examiner_exam_staff_id ON mpw_layout_examiner(exam_staff_id);
CREATE INDEX idx_mpw_layout_examiner_uid ON mpw_layout_examiner(uid);
CREATE INDEX idx_mpw_layout_examiner_logic_exr_no ON mpw_layout_examiner(logic_exr_no);

-- mpw_layout_inspector 表索引
CREATE INDEX idx_mpw_layout_inspector_ex_id ON mpw_layout_inspector(ex_id);
CREATE INDEX idx_mpw_layout_inspector_org_id ON mpw_layout_inspector(org_id);
CREATE INDEX idx_mpw_layout_inspector_exam_staff_id ON mpw_layout_inspector(exam_staff_id);
CREATE INDEX idx_mpw_layout_inspector_uid ON mpw_layout_inspector(uid);
CREATE INDEX idx_mpw_layout_inspector_inspect_org ON mpw_layout_inspector(inspect_org);

-- mpw_layout_invigilator 表索引
CREATE INDEX idx_mpw_layout_invigilator_ex_id ON mpw_layout_invigilator(ex_id);
CREATE INDEX idx_mpw_layout_invigilator_exam_staff_id ON mpw_layout_invigilator(exam_staff_id);
CREATE INDEX idx_mpw_layout_invigilator_org_id ON mpw_layout_invigilator(org_id);
CREATE INDEX idx_mpw_layout_invigilator_uid ON mpw_layout_invigilator(uid);
CREATE INDEX idx_mpw_layout_invigilator_physical_exr_id ON mpw_layout_invigilator(physical_exr_id);

-- p_abnormal_behavior 表索引
CREATE INDEX idx_p_abnormal_behavior_uid ON p_abnormal_behavior(uid);

-- p_behavior 表索引
CREATE INDEX idx_p_behavior_uid ON p_behavior(uid);

-- p_code 表索引
CREATE INDEX idx_p_code_uid ON p_code(uid);

-- p_dutycheck 表索引
CREATE INDEX idx_p_dutycheck_uid ON p_dutycheck(uid);

-- p_post 表索引
CREATE INDEX idx_p_post_code ON p_post(code);

-- s_dutycheck 表索引
CREATE INDEX idx_s_dutycheck_uid ON s_dutycheck(uid);

-- s_es_org_datacount 表索引
CREATE INDEX idx_s_es_org_datacount_uid ON s_es_org_datacount(uid);

-- s_randomcheck 表索引
CREATE INDEX idx_s_randomcheck_uid ON s_randomcheck(uid);

-- vis_behavior 表索引
CREATE INDEX idx_vis_behavior_uid ON vis_behavior(uid);

-- vis_behavior_check 表索引
CREATE INDEX idx_vis_behavior_check_uid ON vis_behavior_check(uid);

-- vis_behavior_detail 表索引
CREATE INDEX idx_vis_behavior_detail_uid ON vis_behavior_detail(uid);

-- vis_code 表索引
CREATE INDEX idx_vis_code_uid ON vis_code(uid);

-- vis_config 表索引
CREATE INDEX idx_vis_config_uid ON vis_config(uid);

-- vis_dutycheck 表索引
CREATE INDEX idx_vis_dutycheck_uid ON vis_dutycheck(uid);

-- vis_dutycheck_detail 表索引
CREATE INDEX idx_vis_dutycheck_detail_uid ON vis_dutycheck_detail(uid);

-- vis_dutycheck_response 表索引
CREATE INDEX idx_vis_dutycheck_response_uid ON vis_dutycheck_response(uid);

-- vis_examinee_check_in 表索引
CREATE INDEX idx_vis_examinee_check_in_uid ON vis_examinee_check_in(uid);

-- vis_examroom_abbehavior 表索引
CREATE INDEX idx_vis_examroom_abbehavior_uid ON vis_examroom_abbehavior(uid);

-- vis_examroom_abbehavior_file 表索引
CREATE INDEX idx_vis_examroom_abbehavior_file_uid ON vis_examroom_abbehavior_file(uid);

-- vis_examroom_absence 表索引
CREATE INDEX idx_vis_examroom_absence_uid ON vis_examroom_absence(uid);

-- vis_examroom_behavior_status 表索引
CREATE INDEX idx_vis_examroom_behavior_status_uid ON vis_examroom_behavior_status(uid);

-- vis_examroom_camera_check 表索引
CREATE INDEX idx_vis_examroom_camera_check_uid ON vis_examroom_camera_check(uid);

-- vis_examroom_camera_file 表索引
CREATE INDEX idx_vis_examroom_camera_file_uid ON vis_examroom_camera_file(uid);

-- vis_examroom_camera_status 表索引
CREATE INDEX idx_vis_examroom_camera_status_uid ON vis_examroom_camera_status(uid);

-- vis_instruct 表索引
CREATE INDEX idx_vis_instruct_uid ON vis_instruct(uid);

-- vis_randomcheck_detail 表索引
CREATE INDEX idx_vis_randomcheck_detail_uid ON vis_randomcheck_detail(uid);

-- vis_randomcheck_response 表索引
CREATE INDEX idx_vis_randomcheck_response_uid ON vis_randomcheck_response(uid);

-- vis_rel_abbehavior_examinee 表索引
CREATE INDEX idx_vis_rel_abbehavior_examinee_uid ON vis_rel_abbehavior_examinee(uid);

-- vis_rel_abbehavior_examineer 表索引
CREATE INDEX idx_vis_rel_abbehavior_examineer_uid ON vis_rel_abbehavior_examineer(uid);

-- vis_rel_absence_examinee 表索引
CREATE INDEX idx_vis_rel_absence_examinee_uid ON vis_rel_absence_examinee(uid);

-- vis_rel_behavior_subject 表索引
CREATE INDEX idx_vis_rel_behavior_subject_uid ON vis_rel_behavior_subject(uid);

-- vis_rel_dutycheck_escode 表索引
CREATE INDEX idx_vis_rel_dutycheck_escode_uid ON vis_rel_dutycheck_escode(uid);

-- vis_rel_instruct_user 表索引
CREATE INDEX idx_vis_rel_instruct_user_uid ON vis_rel_instruct_user(uid); 